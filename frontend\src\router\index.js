import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/CodeView',
    name: 'CodeView',
    component: () => import('@/components/CodeView.vue'),
  },
  {
    path: '/CodeStatistice',
    name: 'CodeStatistice',
    component: () => import('@/components/CodeStatistice.vue'),
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router