package com.cet.eem.bll.energysaving.service.task.impl;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.bll.common.dao.node.MeasureByDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.MeasuredbyVo;
import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceChainDetail;
import com.cet.eem.bll.common.model.ext.subject.energysaving.DeviceChainWithSubLayer;
import com.cet.eem.bll.common.service.impl.PecCoreService;
import com.cet.eem.bll.energysaving.dao.aioptimization.ControlModeDao;
import com.cet.eem.bll.energysaving.dao.aioptimizationv2.AIControlModeSwitchingDao;
import com.cet.eem.bll.energysaving.dao.aioptimizationv2.CoolingWaterTempDao;
import com.cet.eem.bll.energysaving.dao.aioptimizationv2.RefrigerationDataIdMapDao;
import com.cet.eem.bll.energysaving.dao.aioptimizationv2.TerminalDeviceDao;
import com.cet.eem.bll.energysaving.dao.weather.DeviceChainDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.handle.AiScheduleConfig;
import com.cet.eem.bll.energysaving.model.aioptimization.plc.ControlMode;
import com.cet.eem.bll.energysaving.model.config.AIControlRedisLock;
import com.cet.eem.bll.energysaving.model.config.PumpFunctionType;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.def.ModelDef;
import com.cet.eem.bll.energysaving.model.def.QuantityDef;
import com.cet.eem.bll.energysaving.model.refrigerationoptimizationv2.*;
import com.cet.eem.bll.energysaving.model.refrigerationoptimizationv2.aicontrol.ControlOperationCollector;
import com.cet.eem.bll.energysaving.model.refrigerationoptimizationv2.aicontrol.EnumControlType;
import com.cet.eem.bll.energysaving.model.refrigerationoptimizationv2.aicontrol.RemoteControlContext;
import com.cet.eem.bll.energysaving.model.refrigerationoptimizationv2.config.RefrigerationConfig;
import com.cet.eem.bll.energysaving.model.refrigerationoptimizationv2.constant.*;
import com.cet.eem.bll.energysaving.model.refrigerationoptimizationv2.po.AIControlModeSwitchingPO;
import com.cet.eem.bll.energysaving.model.refrigerationoptimizationv2.po.CoolingWaterTempSettingPO;
import com.cet.eem.bll.energysaving.model.refrigerationoptimizationv2.vo.PreSetParamVO;
import com.cet.eem.bll.energysaving.model.refrigerationoptimizationv2.vo.TerminalTempVO;
import com.cet.eem.bll.energysaving.model.weather.PumpVo;
import com.cet.eem.bll.energysaving.service.aioptimization.AiPlcControlService;
import com.cet.eem.bll.energysaving.service.task.AiPlcControlServiceV2;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.definition.LoginDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.exception.BusinessBaseException;
import com.cet.eem.common.file.Md5Utils;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.auth.user.QueryUserPassword;
import com.cet.eem.common.model.auth.user.UserVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.common.utils.JsonTransferUtils;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.devicedataservice.api.DataServiceRestApi;
import com.cet.electric.matterhorn.devicedataservice.api.PecCoreConfigRestApi;
import com.cet.electric.matterhorn.devicedataservice.common.entity.control.ParamSet;
import com.cet.electric.matterhorn.devicedataservice.common.entity.control.RemoteControlPointScheme;
import com.cet.electric.matterhorn.devicedataservice.common.entity.control.RemoteControlResult;
import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.DeviceDataIdLogicalId;
import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.MeasurePointData;
import com.cet.electric.matterhorn.devicedataservice.common.entity.station.MeasureNodeWithDeviceIdDTO;
import com.cet.electric.matterhorn.devicedataservice.common.entity.station.RemoteControlPointDTO;
import com.cet.electric.matterhorn.devicedataservice.common.query.auth.UserPasswordParam;
import com.cet.electric.matterhorn.devicedataservice.common.query.control.ControlIdParam;
import com.cet.electric.matterhorn.devicedataservice.common.query.control.MultiRemoteControlPara;
import com.cet.electric.matterhorn.devicedataservice.common.query.control.RemoteControlIdParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.MathUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.cet.electric.notice.common.constant.ClientTypeDefinition.WEB;

/**
 * @Author: dengxingyu
 * @CreateTime: 2025-05-12
 * @Description: 制冷ai控制第二版
 */
@Service
@Slf4j
public class AiPlcControlServiceV2Impl implements AiPlcControlServiceV2 {
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    ControlModeDao controlModeDao;
    @Autowired
    AiPlcControlService aiPlcControlService;
    @Autowired
    QuantityManageService quantityManageService;
    @Autowired
    CommonUtilsService commonUtilsService;
    @Autowired
    DeviceChainDao deviceChainDao;
    @Autowired
    MeasureByDao measureByDao;
    @Autowired
    PecCoreConfigRestApi pecCoreConfigRestApi;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    DataServiceRestApi dataServiceRestApi;
    @Autowired
    AuthUtils authUtils;
    @Autowired
    PecCoreService pecCoreService;
    @Autowired
    AiScheduleConfig aiScheduleConfig;
    @Qualifier("eem-redis-template")
    @Autowired
    RedisTemplate<String, String> redisTemplate;
    @Autowired
    RefrigerationDataIdMapDao refrigerationDataIdMapDao;
    @Autowired
    TerminalDeviceDao terminalDeviceDao;
    @Autowired
    AIControlModeSwitchingDao aiControlModeSwitchingDao;
    @Autowired
    RefrigerationConfig refrigerationConfig;
    @Autowired
    CoolingWaterTempDao coolingWaterTempDao;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    @Override
    // @AI-Generated-start
    public Boolean controlDeviceByAI(OptimizeControlParam optimizeControlParam) {
        Long refrigerationSystemId = optimizeControlParam.getRefrigerationSystemId();
        // 分布式锁的key和value
        String lockKey = "ai_control_lock:" + refrigerationSystemId;
        String lockValue = UUID.randomUUID().toString();
        // 获取一个30s的锁
        try (AIControlRedisLock lock = new AIControlRedisLock(redisTemplate, lockKey, lockValue, RefrigerationConstantDef.REDIS_LOCK_EXPIRE_SECONDS)){
            // 尝试获取分布式锁
            if (!lock.tryLock()) {
                log.warn("{}系统id为{}的AI控制正在执行中，本次调用被拒绝", RefrigerationConstantDef.AI_CONTROL_LOG, refrigerationSystemId);
                return Boolean.FALSE;
            }

            log.info("{}开始执行,当前时间：{}，系统id为{},控制入参：{}", RefrigerationConstantDef.AI_CONTROL_LOG,
                    LocalDateTime.now(), refrigerationSystemId, JsonTransferUtils.toJSONString(optimizeControlParam));
            ControlMode controlMode = controlModeDao.queryControlModeBySystemId(refrigerationSystemId);
            // 检查当前制冷系统是否是AI控制，是ai但plc设备不是，则修改为plc控制
//            if (Boolean.FALSE.equals(checkAndHandleAiControl(refrigerationSystemId, controlMode))) {
//                log.info("{}不是ai控制模式，执行结束", RefrigerationConstantDef.AI_CONTROL_LOG);
//                return Boolean.FALSE;
//            }
            // 检查当前制冷系统的控制是否处于生效时间内
            if (!isEffectiveTime(controlMode)) {
                log.info("{}当前时间不在控制生效时间段内，执行结束", RefrigerationConstantDef.AI_CONTROL_LOG);
                return Boolean.FALSE;
            }

            try {
                // 查询设备连锁信息和遥控测点信息
                RemoteControlContext remoteControlContext = queryDeviceAndControlPoints(optimizeControlParam);
                if (Objects.isNull(remoteControlContext.getControlPoints())) {
                    log.info("{}未查询到设备连锁信息和遥控测点信息", RefrigerationConstantDef.AI_CONTROL_LOG);
                    return Boolean.FALSE;
                }
                // 处理控制参数
                processControlParameters(remoteControlContext, optimizeControlParam.getChainParamList(), refrigerationSystemId);
                return Boolean.TRUE;
            } catch (Exception e) {
                log.error("{}AI控制设备发生异常", RefrigerationConstantDef.AI_CONTROL_LOG, e);
                commonUtilsService.writeUpdateOperationLogs(RefrigerationConstantDef.AI_CONTROL_LOG_TYPE,
                        "制冷ai控制发生异常",
                        JsonTransferUtils.toJSONString(e),
                        LoginDef.USER_ROOT
                );
                return Boolean.FALSE;
            } finally {
                log.info("{}执行结束,系统id为{}", RefrigerationConstantDef.AI_CONTROL_LOG, refrigerationSystemId);
            }
        } catch (Exception lockException) {
            log.error("{}分布式锁操作异常", RefrigerationConstantDef.AI_CONTROL_LOG, lockException);
            return Boolean.FALSE;
        }
    }

    /**
     * 查询plc节点
     * @param refrigerationSystemId
     * @param projectId
     * @return
     */
    private List<BaseVo> getPlcNodes(Long refrigerationSystemId, Long projectId) {
        RefrigeratingSystem refrigeratingSystem = refrigeratingSystemDao.selectById(refrigerationSystemId);
        if (Objects.isNull(refrigeratingSystem)) {
            log.error("系统id为{}没有查到系统内容！", refrigerationSystemId);
            return new ArrayList<>();
        }
        // 查询plc节点
        return aiPlcControlService.queryNodesByMonitor(refrigeratingSystem.getRoomId());
    }

    /**
     * 处理控制参数
     *
     * @param chainParams
     * @param context
     */
    private void processControlParameters(RemoteControlContext context, List<ChainControlParam> chainParams, Long refrigeratingSystemId) {
        // 处理用户认证
        UserVo userVo = authUtils.queryUser(LoginDef.USER_ROOT);
        if (Objects.isNull(userVo)) {
            log.error("获取用户信息失败");
            return;
        }
        // 用户密码
        String password = getPassword(userVo);
        if (Objects.isNull(password)) {
            log.error("密码获取失败");
            return;
        }
        // 处理冷却水温度设定
        handleCoolingWaterTemp(chainParams, refrigeratingSystemId);
        // 处理遥控参数
        ControlOperationCollector collector = new ControlOperationCollector();
        chainParams.forEach(chainParam ->
                processChainParam(chainParam, context, collector)
        );
        // 处理末端设备温度遥调命令
        handleTerminalDeviceControlParam(collector, refrigeratingSystemId);
        // 执行遥控操作
        executeControlOperations(collector, userVo, password);
    }
// @AI-Generated-end

    /**
     * 处理冷却水温度
     * @param chainParams
     * @param refrigeratingSystemId
     */
    private void handleCoolingWaterTemp(List<ChainControlParam> chainParams, Long refrigeratingSystemId) {
        // 查询冷却水温度设定表
        CoolingWaterTempSettingPO coolingWaterTempSettingPO = coolingWaterTempDao.getCoolingWaterTempSettingBySystemId(refrigeratingSystemId);
        if (Objects.isNull(coolingWaterTempSettingPO)) {
            return;
        }
        // 数据不存在或者不启用温度设定，直接返回
        if (Objects.isNull(coolingWaterTempSettingPO.getCoolingWaterTempStatus()) || !coolingWaterTempSettingPO.getCoolingWaterTempStatus()) {
            return;
        }
        // 给每个连锁中添加冷却水温度设定
        chainParams.forEach(chainParam -> {
            List<OptimizeVariableParam> optimizeVariableParamList = chainParam.getOptimizeVariableParamList();
            Optional<OptimizeVariableParam> first = optimizeVariableParamList.stream().filter(optimizeVariableParam -> Objects.equals(optimizeVariableParam.getOptimizeVariable(), EnumOptimizeVariable.COOLING_WATER_TEMP.getId())).findFirst();
            if (first.isPresent()) {
                return;
            }
            optimizeVariableParamList.add(new OptimizeVariableParam(EnumOptimizeVariable.COOLING_WATER_TEMP.getId(), coolingWaterTempSettingPO.getCoolingWaterTempValue()));
        });
    }

    /**
     * 末端设备温度遥调参数处理
     * @param collector
     */
    private void handleTerminalDeviceControlParam(ControlOperationCollector collector, Long refrigeratingSystemId) {
        // ai控制模式查询，舒适、经济、励志
        AIControlModeSwitchingPO aiControlModeSwitching = aiControlModeSwitchingDao.getAIControlModeSwitchingBySystemId(refrigeratingSystemId);
        // 获取末端设备温度设定
        List<TerminalTempVO> terminalTempVOList = terminalDeviceDao.getTerminalDeviceTemp(refrigeratingSystemId, aiControlModeSwitching.getOptimizationMode());
        if (CollectionUtils.isEmpty(terminalTempVOList)) {
            log.info("{}没有匹配的终端设备温度设定", RefrigerationConstantDef.AI_CONTROL_LOG);
            return;
        }
        // 查询末端设备遥控点
        List<Integer> deviceIds = terminalTempVOList.stream().map(terminalTempVO -> terminalTempVO.getDeviceId().intValue()).distinct().collect(Collectors.toList());
        ApiResult<List<RemoteControlPointScheme>> controlPointsResult = pecCoreConfigRestApi.getRemoteControlPointsScheme(deviceIds);
        if (!controlPointsResult.isSuccess()) {
            log.error("获取终端设备遥控点信息失败");
            return;
        }
        List<RemoteControlPointScheme> pointSchemeList = controlPointsResult.getData();
        if (Objects.isNull(refrigerationConfig.getTerminalDevicePoint())) {
            log.info("{}没有配置末端设备温度控制测点，不下发末端设备温度命令", RefrigerationConstantDef.AI_CONTROL_LOG);
            return;
        }
        terminalTempVOList.forEach(terminalTempVO -> {
            Optional<RemoteControlPointScheme> first = pointSchemeList.stream().filter(pointScheme -> Objects.equals(pointScheme.getDeviceId(), terminalTempVO.getDeviceId().intValue()) && Objects.equals(pointScheme.getDataId(), refrigerationConfig.getTerminalDevicePoint())).findFirst();
            if (!first.isPresent()) {
                return;
            }
            createRemoteControlIdParam(Collections.singletonList(first.get()), collector.getRemoteParams(), terminalTempVO.getTemperature());
        });
    }
    /**
     * 查询设备信息并获取遥控测点信息
     * @param optimizeControlParam
     * @return
     */
    private RemoteControlContext  queryDeviceAndControlPoints(OptimizeControlParam optimizeControlParam) {
        RemoteControlContext remoteControlContext = new RemoteControlContext();
        // 获取连锁
        List<ChainControlParam> chainParamList =  Optional.ofNullable(optimizeControlParam.getChainParamList())
                .orElse(Collections.emptyList());
        List<Long> chainIds = chainParamList.stream().map(ChainControlParam::getChainId).collect(Collectors.toList());

        List<DeviceChainWithSubLayer> deviceChains = deviceChainDao.queryDeviceChainWithDetail(chainIds, optimizeControlParam.getProjectId());

        // 处理连锁数据
        Map<Long, List<DeviceChainDetail>> deviceChainMap = deviceChains.stream().collect(Collectors.toMap(
                DeviceChainWithSubLayer::getId,
                DeviceChainWithSubLayer::getDeviceChainDetails,
                (existing, replacement) -> existing));

        remoteControlContext.setDeviceChainMap(deviceChainMap);
        // 获取plc的管网设备
        Set<BaseVo> plcNodeSet = new LinkedHashSet<>(getPlcNodes(optimizeControlParam.getRefrigerationSystemId(), optimizeControlParam.getProjectId()));
        // 获取所有连锁中的所有管网设备
        Set<BaseVo> baseVos = deviceChains.stream()
                .flatMap(chain -> chain.getDeviceChainDetails().stream())
                .map(detail -> new BaseVo(detail.getObjectId(), detail.getObjectLabel()))
                .collect(Collectors.toSet());
        baseVos.addAll(plcNodeSet);
        // 处理泵设备信息（合并遍历）
        List<PumpVo> pumpVoList = nodeDao.queryNodes(baseVos.stream().filter(vo -> NodeLabelDef.PUMP.equals(vo.getModelLabel())).collect(Collectors.toList()),
                PumpVo.class);

        //区分出冷冻泵和冷却泵
        Map<Integer, List<Long>> pumpIdsByType = pumpVoList.stream().collect(Collectors.groupingBy(PumpVo::getFunctionType, Collectors.mapping(PumpVo::getId, Collectors.toList())));
        remoteControlContext.setCoolingPumpIds(pumpIdsByType.getOrDefault(PumpFunctionType.COOLING_PUMP, Collections.emptyList()));
        remoteControlContext.setFreezingPumpIds(pumpIdsByType.getOrDefault(PumpFunctionType.REFRIGERATING_PUMP, Collections.emptyList()));
        // 获取关联的采集设备
        List<MeasuredbyVo> measuredbyList = measureByDao.queryMeasureBy(new ArrayList<>(baseVos));
        List<Integer> deviceIds = measuredbyList.stream().map(vo -> vo.getMeasuredby().intValue()).distinct().collect(Collectors.toList());

        // 遥控点和测点
        ApiResult<List<RemoteControlPointScheme>> controlPointsResult = pecCoreConfigRestApi.getRemoteControlPointsScheme(deviceIds);
        if (!controlPointsResult.isSuccess()) {
            log.error("{}获取遥控点或测点数据失败:{}", RefrigerationConstantDef.AI_CONTROL_LOG, JsonTransferUtils.toJSONString(controlPointsResult));
            return remoteControlContext;
        }
        List<RemoteControlPointScheme> controlPoints = controlPointsResult.getData();

        // 构建采集设备和管网设备的映射
        Map<BaseVo, Set<Integer>> measuredbyMap = measuredbyList.stream()
                .collect(Collectors.groupingBy(
                        vo -> new BaseVo(vo.getMonitoredid(), vo.getMonitoredlabel()),
                        Collectors.mapping(vo -> vo.getMeasuredby().intValue(), Collectors.toSet())));

        Map<BaseVo, List<RemoteControlPointScheme>> controlPointMap = new HashMap<>(16);

        measuredbyMap.forEach((baseVo, deviceIdsSet) -> {
            // 构建管网设备和遥控点的映射
            controlPointMap.put(baseVo, controlPoints.stream().filter(p -> deviceIdsSet.contains(p.getDeviceId())).collect(Collectors.toList()));
        });
        remoteControlContext.setControlPoints(controlPointMap);
        // 查询测点映射数据
        List<RefrigerationDataIdMapVO> dataIdMapByNodeList = refrigerationDataIdMapDao.getDataIdMapByNodeList(baseVos);
        remoteControlContext.setDataIdMapVOList(dataIdMapByNodeList);
        return remoteControlContext;
    }

    /**
     * 执行遥控操作
     * @param collector
     * @param userVo
     * @param password
     */
    private void executeControlOperations(ControlOperationCollector collector, UserVo userVo, String password) {
        userVo.setPassword(password);
        Map<Integer, RemoteControlIdParam> remoteControlIdParamMap = collector.getRemoteParams();
        // 获取控制命令的执行顺序
        List<String> controlOrderList = getControlOrder();
        for (int i =0 ; i < controlOrderList.size(); i++) {
            String controlOrder = controlOrderList.get(i);
            EnumControlType enumControlType = EnumControlType.getByCommand(controlOrder);
            if (Objects.isNull(enumControlType)) {
                log.error("控制命令{}不存在", controlOrder);
                return;
            }
            switch (enumControlType) {
                case START_CONTROL:
                    // 开机
                case STOP_CONTROL:
                    // 关机
                    executeStartOrStopControl(collector, userVo, enumControlType, i, controlOrderList);
                    break;
                case TELEADJUSTING_CONTROL:
                    // 遥调
                    executeTeleAdjustingControl(remoteControlIdParamMap, userVo, password);
                    break;
                default:
                    log.error("控制命令{}不存在", controlOrder);
                    break;
            }
        };
    }

    /**
     * 执行遥控命令，开机或关机
     * @param collector
     * @param userVo
     */
    // @AI-Generated-start
    private Boolean executeStartOrStopControl(ControlOperationCollector collector, UserVo userVo,  EnumControlType enumControlType, Integer index, List<String> controlOrderList) {
        // 使用变量避免重复判断操作类型
        final boolean isStart = Objects.equals(EnumControlType.START_CONTROL, enumControlType);
        List<Integer> paramIds = isStart ? collector.getStartParams() : collector.getStopParams();
        if (CollectionUtils.isEmpty(paramIds)) {
            log.warn("{}无{}命令下发，参数为空", RefrigerationConstantDef.AI_CONTROL_LOG, enumControlType.getDescription());
            return false;
        }
        MultiRemoteControlPara remoteControlPara = createControlParam(paramIds, userVo);
        log.info( "{}下发{}命令，参数为{}", RefrigerationConstantDef.AI_CONTROL_LOG, enumControlType.getDescription(), JsonTransferUtils.toJSONString(remoteControlPara));
        dataServiceRestApi.batchRemoteControlByPoints(remoteControlPara, true, null);

        // 记录日志
        commonUtilsService.writeUpdateOperationLogs(RefrigerationConstantDef.AI_CONTROL_LOG_TYPE,
                String.format("下发%s命令", enumControlType.getDescription()),
                JsonTransferUtils.toJSONString(remoteControlPara),
                userVo.getId()
        );

        // 开机和关机指令， 等待指定时间后检查冷机状态
        Integer interval = isStart ? aiScheduleConfig.getStartStopInterval() : aiScheduleConfig.getStopInterval();
        // 无有效等待间隔时直接返回失败
        if (interval == null || interval <= 0) {
            return  Boolean.FALSE;
        }
        log.info("{}已下发{}命令，等待{}秒后检查冷机状态", RefrigerationConstantDef.AI_CONTROL_LOG, enumControlType.getDescription(), interval);
        try {
            Thread.sleep(interval * TimeUtil.SECOND);
        } catch (InterruptedException e) {
            log.error("{}执行等待时发生中断", RefrigerationConstantDef.AI_CONTROL_LOG, e);
            Thread.currentThread().interrupt();
            return Boolean.FALSE;  // 中断后直接返回失败
        }

        // 添加状态检查的异常捕获
        try {
            return checkStartOrStopStatus(collector, enumControlType, index, controlOrderList);
        } catch (Exception e) {
            log.error("{}冷机状态检查异常: {}", RefrigerationConstantDef.AI_CONTROL_LOG, e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * 检查开机和关机的状态
     * @param collector
     * @param enumControlType
     * @param index
     * @param controlOrderList
     * @return
     */
    private Boolean checkStartOrStopStatus(ControlOperationCollector collector, EnumControlType enumControlType, Integer index, List<String> controlOrderList) {
        Boolean commandSuccess = Boolean.FALSE;
        // 查询冷机状态测点的实时数据
        List<BaseVo> nodeList = Objects.equals(EnumControlType.START_CONTROL, enumControlType) ? collector.getStartNodeList() : collector.getStopNodeList();
        List<MeasurePointData> statusDataList = queryRealTimeData(nodeList, Collections.singletonList(QuantityDataIdEnum.HOST_CHILLER_STATUSES.getDataId()));
        // 开机状态为1，关机状态为0
        Double compareValue = Objects.equals(EnumControlType.START_CONTROL, enumControlType) ? RefrigerationConstantDef.TRUE_DATA : RefrigerationConstantDef.FALSE_DATA;
        Boolean isSuccess = statusDataList.stream()
                .filter(pointData -> Objects.nonNull(pointData.getValue()))
                .anyMatch(pointData -> MathUtils.equals(pointData.getValue(), compareValue));
        if (!statusDataList.isEmpty() && isSuccess) {
            commandSuccess = Boolean.TRUE;
            log.info("{}冷机{}命令成功", RefrigerationConstantDef.AI_CONTROL_LOG, enumControlType.getDescription());
        } else {
            // 冷机启动失败则不下发关机指令
            log.warn("{}冷机{}命令失败",  RefrigerationConstantDef.AI_CONTROL_LOG, enumControlType.getDescription());
            commonUtilsService.writeUpdateOperationLogs(RefrigerationConstantDef.AI_CONTROL_LOG_TYPE,
                    String.format("%s没有成功", enumControlType.getDescription()),
                    JsonTransferUtils.toJSONString(statusDataList),
                    LoginDef.USER_ROOT
            );
            // 如果是开机指令，检查后续是否有关机指令
            if (Objects.equals(EnumControlType.START_CONTROL, enumControlType) && hasPendingStopCommand(controlOrderList, index)) {
                log.info("{}检测到后续有关机指令，移除关机指令", RefrigerationConstantDef.AI_CONTROL_LOG);
                removePendingStopCommands(collector); // 需要实现从collector移除待停止命令的方法
            }
        }
        return commandSuccess;
    }
    // 辅助方法：检查后续是否有停止命令
    private Boolean hasPendingStopCommand(List<String> controlOrderList, int currentIndex) {
        return controlOrderList.subList(currentIndex + 1, controlOrderList.size())
                .contains(EnumControlType.STOP_CONTROL.getCommand());
    }

    // 辅助方法：移除待处理的停止命令
    private void removePendingStopCommands(ControlOperationCollector collector) {
        collector.getStopParams().clear();
    }
    // @AI-Generated-end


    /**
     * 执行遥调命令
     * @param remoteControlIdParamMap
     * @param userVo
     * @param password
     */
    private void executeTeleAdjustingControl(Map<Integer, RemoteControlIdParam> remoteControlIdParamMap, UserVo userVo, String password) {
        //遥调信息,每次执行一个设备的遥调命令
        remoteControlIdParamMap.forEach((key, param) ->{
            singleTeleAdjustingControl(userVo, param);
        });
    }

    /**
     * 单个设备执行遥调命令
     * @param userVo
     * @param param
     */
    private void singleTeleAdjustingControl(UserVo userVo, RemoteControlIdParam param) {
    List<ControlIdParam> paramIdValues = param.getParamIdValues();
    paramIdValues.forEach(controlIdParam ->
        scheduleRetryWithExecutor(userVo, param, controlIdParam, 0)
    );
    }
    private void scheduleRetryWithExecutor(UserVo userVo,
                                           RemoteControlIdParam param,
                                           ControlIdParam controlIdParam,
                                           int attempt) {
        int maxRetries = aiScheduleConfig.getRetryCount();

        // 立即执行首次尝试
        if (singlePreSetParam(controlIdParam, param, userVo)) {
            return;
        }

        // 安排下一次重试
        if (attempt < maxRetries && aiScheduleConfig.getRetryWait() > 0) {
            scheduler.schedule(() -> {
                if (!Thread.currentThread().isInterrupted()) {
                    scheduleRetryWithExecutor(userVo, param, controlIdParam, attempt + 1);
                } else {
                    log.warn("{}重试任务被中断", RefrigerationConstantDef.AI_CONTROL_LOG);
                }
            }, aiScheduleConfig.getRetryWait(), TimeUnit.SECONDS);
        }
    }

    @PreDestroy
    public void shutdownScheduler() {
        scheduler.shutdownNow(); // 立即终止所有任务
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                log.error("{}线程池未完全关闭", RefrigerationConstantDef.AI_CONTROL_LOG);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }



    /**
     * 预置参数，单个设备单个参数
     * @param controlIdParam
     * @param param
     * @param userVo
     * @return
     */
    private Boolean singlePreSetParam(ControlIdParam controlIdParam, RemoteControlIdParam param, UserVo userVo) {
        RemoteControlIdParam singleParam = new RemoteControlIdParam();
        BeanUtils.copyProperties(param, singleParam);
        singleParam.setParamIdValues(Collections.singletonList(controlIdParam));
        Long now  = System.currentTimeMillis();
        String md5Hex = DigestUtils.md5DigestAsHex(
                String.format("%s#%s#%d", "CET_Matterhorn", userVo.getPassword(), now)
                        .getBytes(StandardCharsets.UTF_8));
        UserPasswordParam userInfo = new UserPasswordParam(userVo.getName(), md5Hex, WEB, now);
        singleParam.setUserInfo(userInfo);
        singleParam.setParamIdValues(Collections.singletonList(controlIdParam));
        try {
            ApiResult<Boolean> booleanApiResult = dataServiceRestApi.remoteControlByParamId(singleParam, true, null);
            if (booleanApiResult.isSuccess()) {
                log.info("{}遥调成功, 接口入参：{}，接口返回信息：{}",  RefrigerationConstantDef.AI_CONTROL_LOG, JsonTransferUtils.toJSONString(singleParam), JsonTransferUtils.toJSONString(booleanApiResult));
                commonUtilsService.writeUpdateOperationLogs(RefrigerationConstantDef.AI_CONTROL_LOG_TYPE,
                        "遥调成功",
                        JsonTransferUtils.toJSONString(param),
                        LoginDef.USER_ROOT
                );
                return true;
            } else {
                log.info("{}遥调失败，接口入参：{}，接口返回信息：{}", RefrigerationConstantDef.AI_CONTROL_LOG, JsonTransferUtils.toJSONString(singleParam), JsonTransferUtils.toJSONString(booleanApiResult));
                commonUtilsService.writeUpdateOperationLogs(RefrigerationConstantDef.AI_CONTROL_LOG_TYPE,
                        "遥调失败",
                        JsonTransferUtils.toJSONString(param),
                        LoginDef.USER_ROOT
                );
                return false;
            }
        } catch (Exception e) {
            log.error("{}执行遥调命令发生异常", RefrigerationConstantDef.AI_CONTROL_LOG, e);
            return false;
        }
    }

    /**
     * 获取控制命令执行顺序
     * @return
     */
    private List<String> getControlOrder() {
        String controlOrder = aiScheduleConfig.getControlOrder();
        if (StringUtils.isEmpty(controlOrder)) {
            log.error("控制顺序不能为空");
            return new ArrayList<>();
        }
        String[] split = controlOrder.split(",");
        return Arrays.stream(split).collect(Collectors.toList());
    }

    /**
     * 处理一个连锁下的遥控参数
     * @param chainParam
     * @param context
     * @param collector
     */
    private void processChainParam(ChainControlParam chainParam, RemoteControlContext context, ControlOperationCollector collector) {
        if (CollectionUtils.isEmpty(chainParam.getOptimizeVariableParamList())) {
            log.warn("连锁{}没有优化变量", chainParam.getChainId());
            return;
        }
        chainParam.getOptimizeVariableParamList().forEach(varParam ->
                processVariableParam(chainParam, varParam, context, collector)
        );
    }

    /**
     * 按照优化变量处理遥控参数
     * @param chainParam
     * @param varParam
     * @param context
     * @param collector
     */
    private void processVariableParam(ChainControlParam chainParam, OptimizeVariableParam varParam,
                                      RemoteControlContext context, ControlOperationCollector collector) {
        EnumOptimizeVariable variable = EnumOptimizeVariable.getById(varParam.getOptimizeVariable());
        if (variable == null) {
            log.warn("{}未知优化变量ID: {}", RefrigerationConstantDef.AI_CONTROL_LOG, varParam.getOptimizeVariable());
            return;
        }

        switch (variable) {
            case CHILLED_PUMP_FREQUENCY:
                // 冷冻水泵频率
                handlePumpControl(chainParam, varParam, context, collector, RemoteControlPointDef.FREEZING_PUMP_FREQUENCY);
                break;
            case CHILLED_WATER_TEMP_DIFF:
                // 冷冻水温差
                handlePlcControl(varParam, context, collector, RemoteControlPointDef.FREEZING_PUMP_TEMP_DIFF);
                break;
            case COOLING_WATER_TEMP:
                // 冷却水温度
                handlePlcControl(varParam, context, collector, RemoteControlPointDef.COOLING_PUMP_TEMP);
                break;
            case COOLING_WATER_TEMP_DIFF:
                // 冷却水温差
                handlePlcControl(varParam, context, collector, RemoteControlPointDef.COOLING_PUMP_TEMP_DIFF);
                break;
            case COOLING_WATER_PUMP_FREQUENCY:
                // 冷却水泵频率
                handlePumpControl(chainParam, varParam, context, collector, RemoteControlPointDef.COOLING_PUMP_FREQUENCY);
                break;
            case CHILLED_WATER_TEMP:
                // 冷冻水温度设定
                handleMainEngineControl(chainParam, varParam, context, collector, RemoteControlPointDef.COLDWATERMAINENGINE_WATER_TEMP_SETTING);
                break;
            case START_STOP_STATUS:
                // 主机开关机状态
                handleStartStopControl(chainParam, varParam, context, collector);
                break;
            default:
                log.warn("{}未处理的优化变量类型: {}", RefrigerationConstantDef.AI_CONTROL_LOG, varParam.getOptimizeVariable());
        }
    }

    /**
     * 处理plc遥调参数
     * @param varParam
     * @param context
     * @param collector
     * @param pointDef
     */
    private void handlePlcControl(OptimizeVariableParam varParam, RemoteControlContext context, ControlOperationCollector collector, Integer pointDef) {
        // plc的管网设备
        Map<BaseVo, List<RemoteControlPointScheme>> controlPointMap = context.getControlPoints();
        List<BaseVo> plcNodes = controlPointMap.keySet().stream().filter(node -> Objects.equals(node.getModelLabel(), NodeLabelDef.PLC)).collect(Collectors.toList());
        // 获取遥控点位
        List<RemoteControlPointScheme> controlPointSchemeList = getRemoteControlPoint(context, plcNodes, pointDef);
        // 组装遥测参数
        createRemoteControlIdParam(controlPointSchemeList, collector.getRemoteParams(), varParam.getValue());
    }

    /**
     * 获取映射的测点
     * @param pointDef 标准测点
     * @param context 遥控点、映射点、节点等信息
     * @param nodes 管网节点
     * @return
     */
    private Map<BaseVo, MapDataIdVo> getMapDataId(Integer pointDef, RemoteControlContext context, Set<BaseVo> nodes) {
        // 映射文件里面的节点
        List<RefrigerationDataIdMapVO> dataIdMapVOList = context.getDataIdMapVOList();
        if (CollectionUtils.isEmpty(dataIdMapVOList)) {
            return Collections.emptyMap();
        }
        // 过滤出目标节点的映射点信息
        List<RefrigerationDataIdMapVO> filterDataIdMapNodeList = dataIdMapVOList.stream().filter(dataIdMapVO -> nodes.contains(new BaseVo(dataIdMapVO.getObjectId(), dataIdMapVO.getObjectLabel()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterDataIdMapNodeList)) {
            return Collections.emptyMap();
        }
        Map<BaseVo, MapDataIdVo> nodeDataIdMap = new HashMap<>(16);
        filterDataIdMapNodeList.forEach(dataIdMapVO -> {
            List<DataIdMapDetailVO> dataIdList = dataIdMapVO.getDataIdList();
            if (CollectionUtils.isEmpty(dataIdList)) {
                return;
            }
            // 找出标准测点的映射测点
            Optional<DataIdMapDetailVO> first = dataIdList.stream().filter(dataIdMapDetailVO -> Objects.equals(dataIdMapDetailVO.getOriginDataId(), Long.valueOf(pointDef))).findFirst();
            if (!first.isPresent()) {
                return;
            }
            nodeDataIdMap.put(new BaseVo(dataIdMapVO.getObjectId(), dataIdMapVO.getObjectLabel()), first.get().getMapDataId());
        });
        return nodeDataIdMap;
    }

    /**
     * 处理水泵遥调参数
     * @param chainParam
     * @param varParam
     * @param context
     * @param collector
     * @param pointDef
     */
    private void handlePumpControl(ChainControlParam chainParam, OptimizeVariableParam varParam,
                                   RemoteControlContext context, ControlOperationCollector collector, Integer pointDef) {
        List<Long> pumpIds = Objects.equals(pointDef, RemoteControlPointDef.COOLING_PUMP_FREQUENCY) ? context.getCoolingPumpIds() : context.getFreezingPumpIds();
        // 泵的管网设备
        List<BaseVo> nodes = getPipeNode(context.getDeviceChainMap(), chainParam.getChainId(), NodeLabelDef.PUMP, pumpIds);
        // 获取遥控点位
        List<RemoteControlPointScheme> controlPointSchemeList = getRemoteControlPoint(context, nodes, pointDef);
        // 创建遥控参数
        createRemoteControlIdParam(controlPointSchemeList, collector.getRemoteParams(), varParam.getValue());
    }

    /**
     * 处理主机遥调参数
     * @param chainParam
     * @param varParam
     * @param context
     * @param collector
     * @param point
     */
    private void handleMainEngineControl(ChainControlParam chainParam, OptimizeVariableParam varParam,
                                         RemoteControlContext context, ControlOperationCollector collector,
                                         Integer point) {
        List<BaseVo> nodes = getPipeNode(context.getDeviceChainMap(), chainParam.getChainId(),
                NodeLabelDef.COLD_WATER_MAINENGINE, Collections.emptyList());
        List<RemoteControlPointScheme> controlPointSchemeList = getRemoteControlPoint(context, nodes, point);
        createRemoteControlIdParam(controlPointSchemeList, collector.getRemoteParams(), varParam.getValue());
    }

    /**
     * 处理主机开关机参数
     * @param chainParam
     * @param varParam
     * @param context
     * @param collector
     */
    private void handleStartStopControl(ChainControlParam chainParam, OptimizeVariableParam varParam,
                                        RemoteControlContext context, ControlOperationCollector collector) {
        List<BaseVo> nodes = getPipeNode(context.getDeviceChainMap(), chainParam.getChainId(),
                NodeLabelDef.COLD_WATER_MAINENGINE, Collections.emptyList());
        List<Integer> targetParamIds = getStartTopStatus(context, nodes, varParam);

        if (CollectionUtils.isEmpty(targetParamIds)) {
            return;
        }
        if (MathUtils.equals(varParam.getValue(), RefrigerationConstantDef.TRUE_DATA)) {
            collector.getStartParams().addAll(targetParamIds);
            collector.getStartNodeList().addAll(nodes);
        } else if (MathUtils.equals(varParam.getValue(), RefrigerationConstantDef.FALSE_DATA)) {
            collector.getStopParams().addAll(targetParamIds);
            collector.getStopNodeList().addAll(nodes);
        }
    }

    /**
     * 组装遥控参数
     * @param paramIds
     * @param userVo
     * @return
     */
    private MultiRemoteControlPara createControlParam(List<Integer> paramIds, UserVo userVo) {
        MultiRemoteControlPara multiRemoteControlPara = new MultiRemoteControlPara();
        multiRemoteControlPara.setIds(paramIds);
        multiRemoteControlPara.setClient(WEB);
        multiRemoteControlPara.setUserName(userVo.getName());
        // MD5加密
        Long nowTime = System.currentTimeMillis();
        String md5Hex = DigestUtils.md5DigestAsHex(
                String.format("%s#%s#%d", "CET_Matterhorn", userVo.getPassword(), nowTime)
                        .getBytes(StandardCharsets.UTF_8));
        multiRemoteControlPara.setPassword(md5Hex);
        multiRemoteControlPara.setTimestamps(System.currentTimeMillis());
        return multiRemoteControlPara;
    }

    /**
     * 组装遥测的参数
     * @param controlPointSchemeList
     * @param remoteControlIdParamMap
     * @param value
     */
    // @AI-Generated-start
    private void createRemoteControlIdParam(List<RemoteControlPointScheme> controlPointSchemeList,
                                            Map<Integer, RemoteControlIdParam> remoteControlIdParamMap, Double value) {
        if (CollectionUtils.isEmpty(controlPointSchemeList)) {
            return;
        }
        controlPointSchemeList.forEach(controlPointScheme -> {
            Integer deviceId = controlPointScheme.getDeviceId();
            if (Objects.isNull(deviceId)) {
                return; // 新增空值校验
            }

            RemoteControlIdParam remoteControlIdParam = remoteControlIdParamMap.computeIfAbsent(deviceId, k -> {
                RemoteControlIdParam newParam = new RemoteControlIdParam();
                newParam.setDeviceId(deviceId);
                newParam.setChannelId(controlPointScheme.getChannelId());
                newParam.setStationId(controlPointScheme.getStationId());
                newParam.setType(RefrigerationConstantDef.PRE_SET_PARAMETER);
                newParam.setParamIdValues(new ArrayList<>());
                return newParam;
            });

            List<ControlIdParam> controlIdParamList = remoteControlIdParam.getParamIdValues();
            // 去重添加参数
            Boolean exist = controlIdParamList.stream().anyMatch(controlIdParam -> Objects.equals(controlIdParam.getParamId(), controlPointScheme.getPara()));
            if (!exist) {
                controlIdParamList.add(new ControlIdParam(controlPointScheme.getPara(), value));
            }
        });
    }
    // @AI-Generated-end


    /**
     * 获取遥控点
     * @param context 遥控点
     * @param baseVoList 管网节点
     * @param controlPoint 测点
     * @return
     */
    private List<RemoteControlPointScheme> getRemoteControlPoint(RemoteControlContext context, List<BaseVo> baseVoList, Integer controlPoint) {
        if (CollectionUtils.isEmpty(baseVoList)) {
            return new ArrayList<>();
        }
        // 获取节点的映射测点
        Map<BaseVo, MapDataIdVo> nodeDataIdMap = getMapDataId(controlPoint, context, new HashSet<>(baseVoList));
        Map<BaseVo, List<RemoteControlPointScheme>> nodeControlPointMap = context.getControlPoints();
        // 获取官网设备的遥控点和测点
        List<RemoteControlPointScheme> allPointDTOList = new ArrayList<>();
        baseVoList.forEach(baseVo -> {
            // 单个管网节点的遥控点
            List<RemoteControlPointScheme> pointDTOList = nodeControlPointMap.get(baseVo);
            if (CollectionUtils.isEmpty(pointDTOList)) {
                log.error("{}{}没有关联遥控点或测点！", RefrigerationConstantDef.AI_CONTROL_LOG, JsonTransferUtils.toJSONString(baseVoList));
                return;
            }
            // 映射测点
            MapDataIdVo mapDataIdVo = nodeDataIdMap.get(baseVo);
            if (Objects.nonNull(mapDataIdVo)) {
                //映射测点存在则直接取映射测点
                Optional<RemoteControlPointScheme> first = pointDTOList.stream().filter(pointDTO -> Objects.equals(Long.valueOf(pointDTO.getDataId()), mapDataIdVo.getDataId()) && Objects.equals(pointDTO.getLogicalId(), mapDataIdVo.getLogicalId())).findFirst();
                first.ifPresent(allPointDTOList::add);
                return;
            }
            // 标准测点，可能存在多个回路号，标准测点就取一个
            Optional<RemoteControlPointScheme> optional = pointDTOList.stream().filter(pointDTO -> Objects.equals(pointDTO.getDataId(), controlPoint)).findFirst();
            optional.ifPresent(allPointDTOList::add);
        });
        return allPointDTOList;
    }

    /**
     * 获取冷机的启停状态遥控点
     * @param context
     * @param baseVoList
     * @param optimizeVariableParam
     */
    private List<Integer> getStartTopStatus(RemoteControlContext context, List<BaseVo> baseVoList, OptimizeVariableParam optimizeVariableParam) {
        List<Integer> startopStatusList = new ArrayList<>();
        if (CollectionUtils.isEmpty(baseVoList)) return new ArrayList<>();
        // 找到映射的开机或关机遥控点
        Map<BaseVo, MapDataIdVo> nodeDataIdMap = getMapDataId(
                MathUtils.equals(optimizeVariableParam.getValue(), RefrigerationConstantDef.TRUE_DATA) ? RefrigerationStandardDataIdDef.HOST_ONE_KEY_START : RefrigerationStandardDataIdDef.HOST_ONE_KEY_STOP,
                context,
                new HashSet<>(baseVoList));
        Map<BaseVo, List<RemoteControlPointScheme>> nodeControlPointMap = context.getControlPoints();
        // 找到每个管网节点的遥控点
        baseVoList.forEach(baseVo -> {
            List<RemoteControlPointScheme> controlPointSchemeList = nodeControlPointMap.get(baseVo);
            MapDataIdVo mapDataIdVo = nodeDataIdMap.get(baseVo);
            // 找到状态量的测点
            if (MathUtils.equals(optimizeVariableParam.getValue(), RefrigerationConstantDef.TRUE_DATA)) {
                // 开机遥控点
                RemoteControlPointScheme controlPointScheme = getRemoteControlPointDTO(controlPointSchemeList, RefrigerationStandardDataIdDef.HOST_ONE_KEY_START, mapDataIdVo);
                if (Objects.isNull(controlPointScheme)) return;
                startopStatusList.add(controlPointScheme.getNodeId());
            }
            // 关机遥控点
            if (MathUtils.equals(optimizeVariableParam.getValue(), RefrigerationConstantDef.FALSE_DATA)) {
                RemoteControlPointScheme controlPointScheme = getRemoteControlPointDTO(controlPointSchemeList, RefrigerationStandardDataIdDef.HOST_ONE_KEY_STOP, mapDataIdVo);
                if (Objects.isNull(controlPointScheme)) return;
                startopStatusList.add(controlPointScheme.getNodeId());
            }
        });
        return startopStatusList;
    }

    /**
     * 获取管网设备节点
     * @param deviceChainMap
     * @param chainId
     * @return
     */
    private List<BaseVo> getPipeNode(Map<Long, List<DeviceChainDetail>> deviceChainMap, Long chainId, String modelLabel, List<Long> pumpIdList) {
        // 根据连锁id找到冷机的启停参数
        List<DeviceChainDetail> deviceChainDetails = deviceChainMap.get(chainId);
        if (CollectionUtils.isEmpty(deviceChainDetails)) {
            return new ArrayList<>();
        }
        // 找到冷机
        List<DeviceChainDetail> deviceChainDetailList = deviceChainDetails.stream().filter(deviceChainDetail -> Objects.equals(deviceChainDetail.getObjectLabel(), modelLabel)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceChainDetailList)) {
            return new ArrayList<>();
        }
        // 过滤特定类型的泵
        if (Objects.equals(modelLabel, NodeLabelDef.PUMP)) {
            deviceChainDetailList = deviceChainDetailList.stream().filter(deviceChainDetail -> pumpIdList.contains(deviceChainDetail.getObjectId())).collect(Collectors.toList());
        }
        return deviceChainDetailList.stream().map(deviceChainDetail -> new BaseVo(deviceChainDetail.getObjectId(), deviceChainDetail.getObjectLabel())).distinct().collect(Collectors.toList());
    }

    /**
     * 获取远程遥控点
     * @param pointDTOList
     * @param dataId
     * @return
     */
    private RemoteControlPointScheme getRemoteControlPointDTO(List<RemoteControlPointScheme> pointDTOList, Integer dataId, MapDataIdVo mapDataIdVo) {
        if (Objects.isNull(mapDataIdVo)) {
            return pointDTOList.stream().filter(pointDTO -> Objects.equals(pointDTO.getDataId(), dataId)).findFirst().orElse(null);
        }
        Optional<RemoteControlPointScheme> pointDTOOptional = pointDTOList.stream().filter(remoteControlPointDTO -> Objects.equals(Long.valueOf(remoteControlPointDTO.getDataId()), mapDataIdVo.getDataId()) && Objects.equals(remoteControlPointDTO.getLogicalId(), mapDataIdVo.getLogicalId())).findFirst();
        return pointDTOOptional.orElse(null);
    }

    /**
     * 获取明文密码
     * @param userVo
     * @return
     */
    @Override
    public String getPassword(UserVo userVo) {
        Long nowTime = System.currentTimeMillis();
        // 密文密码
        String password = authUtils.getPassword(userVo.getId());
        QueryUserPassword queryUserPassword = new QueryUserPassword();
        // MD5加密
        String md5Hex = DigestUtils.md5DigestAsHex(
                String.format("%s#%s#%d", "CET_Matterhorn", password, nowTime)
                        .getBytes(StandardCharsets.UTF_8));

        // 查询明文密码
        queryUserPassword.setPassword(password);
        queryUserPassword.setMd5(md5Hex);
        queryUserPassword.setTimestamps(nowTime);
        return authUtils.getDecryptPassword(queryUserPassword);
    }

    @Override
    public Boolean controlSingleDevice(BaseVo baseVo, Set<Integer> pointSet, Long userId) {
        List<RemoteControlPointScheme> pointSchemeList = getRemoteControlPointSchemes(Collections.singletonList(baseVo));
        if (CollectionUtils.isEmpty(pointSchemeList)) {
            log.warn("{}没有找到遥控点", baseVo.getId() + CommonUtils.UNDER_LINE + baseVo.getModelLabel());
            return false;
        }
        List<Integer> targetPointList = pointSchemeList.stream().filter(pointScheme -> pointSet.contains(pointScheme.getDataId()))
                .map(RemoteControlPointScheme::getNodeId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(targetPointList)) {
            log.warn("{}没有找到目标点", baseVo.getId() + CommonUtils.UNDER_LINE + baseVo.getModelLabel());
            return false;
        }
        UserVo userVo = authUtils.queryUser(userId);
        userVo.setPassword(getPassword(userVo));
        MultiRemoteControlPara controlParam = createControlParam(targetPointList, userVo);
        ApiResult<RemoteControlResult> remoteControlResultApiResult = dataServiceRestApi.batchRemoteControlByPoints(controlParam, true, null);
        if (!remoteControlResultApiResult.isSuccess()) {
            log.error("{}下发遥控命令报错{}", baseVo.getId() + CommonUtils.UNDER_LINE + baseVo.getModelLabel(), remoteControlResultApiResult.getMsg());
            throw new BusinessBaseException("下发遥控命令出错");
        }
        return true;
    }

    /**
     * 获取遥控点
     * @param baseVoList
     * @return
     */
    private List<RemoteControlPointScheme> getRemoteControlPointSchemes(List<BaseVo> baseVoList) {
        List<MeasuredbyVo> measuredbyList = measureByDao.queryMeasureBy(baseVoList);
        List<Integer> deviceIds = measuredbyList.stream().map(vo -> vo.getMeasuredby().intValue()).distinct().collect(Collectors.toList());

        // 遥控点和测点
        ApiResult<List<RemoteControlPointScheme>> controlPointsResult = pecCoreConfigRestApi.getRemoteControlPointsScheme(deviceIds);
        if (!controlPointsResult.isSuccess()) {
            log.error("{}获取遥控点或测点数据失败:{}", RefrigerationConstantDef.AI_CONTROL_LOG, JsonTransferUtils.toJSONString(controlPointsResult));
            return new ArrayList<>();
        }
        return controlPointsResult.getData();
    }

    @Override
    public Boolean preSetParameters(List<PreSetParamVO> preSetParamVOList, Long userId) {
        List<BaseVo> baseVoList = preSetParamVOList.stream().map(preSetParamVO -> new BaseVo(preSetParamVO.getObjectId(), preSetParamVO.getObjectLabel())).collect(Collectors.toList());
        // 查询遥控点
        List<RemoteControlPointScheme> remoteControlPointSchemes = getRemoteControlPointSchemes(baseVoList);
        if (CollectionUtils.isEmpty(remoteControlPointSchemes)) {
            log.warn("{}没有找到遥控点", JsonTransferUtils.toJSONString(baseVoList));
            return false;
        }
        // 过滤遥控点
        Set<Integer> pointSet = preSetParamVOList.stream().map(PreSetParamVO::getPointId).collect(Collectors.toSet());
        List<RemoteControlPointScheme> targetPointSchemeList = remoteControlPointSchemes.stream().filter(remoteControlPointScheme -> pointSet.contains(remoteControlPointScheme.getDataId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(targetPointSchemeList)) {
            log.warn("{}没有找到目标遥控点", JsonTransferUtils.toJSONString(baseVoList));
            return false;
        }
        // 查用户密码
        UserVo userVo = authUtils.queryUser(userId);
        userVo.setPassword(getPassword(userVo));
        Map<Integer, RemoteControlIdParam> remoteControlIdParamMap = new HashMap<>(16);
        // 组装遥调参数
        targetPointSchemeList.forEach(remoteControlPointScheme -> {
            Optional<PreSetParamVO> first = preSetParamVOList.stream().filter(preSetParamVO -> Objects.equals(preSetParamVO.getPointId(), remoteControlPointScheme.getDataId())).findFirst();
            if (!first.isPresent()) {
                log.warn("{}没有找到目标点{}", JsonTransferUtils.toJSONString(baseVoList), remoteControlPointScheme.getDataId());
                return;
            }
            createRemoteControlIdParam(Collections.singletonList(remoteControlPointScheme), remoteControlIdParamMap, first.get().getValue());
        });
        // 下发遥调命令，只能单个设备单个参数的下发
        remoteControlIdParamMap.forEach((key, value) -> {
            List<ControlIdParam> paramIdValues = value.getParamIdValues();
            paramIdValues.forEach(paramIdValue -> {
                Boolean isSuccess = singlePreSetParam(paramIdValue, value, userVo);
                if (!isSuccess) {
                    log.error("{}预置参数{}失败", JsonTransferUtils.toJSONString(baseVoList), paramIdValue);
                    throw new BusinessBaseException("下发遥控参数失败");
                }
            });
        });
        return true;
    }

    /**
     * 校验制冷系统是否是ai控制，并做一定的模式修正
     * @param refrigerationSystemId
     * @return
     */
    // @AI-Generated-start
    private Boolean checkAndHandleAiControl(Long refrigerationSystemId, ControlMode controlMode) {
    if (Objects.isNull(controlMode) || !Objects.equals(controlMode.getMode(), ModelDef.AI)) {
        return false;
    }

    // 查询制冷系统
    RefrigeratingSystem refrigeratingSystem = queryRefrigeratingSystem(refrigerationSystemId, controlMode);
    if (Objects.isNull(refrigeratingSystem)) {
        return false;
    }

    // 查询PLC节点
    List<BaseVo> plcNodes = aiPlcControlService.queryNodesByMonitor(refrigeratingSystem.getRoomId());
    if (CollectionUtils.isEmpty(plcNodes)) {
        log.error("系统id为{}没有查到plc节点内容！", refrigerationSystemId);
        return false;
    }

    // 检查plc设备的控制模式是否是ai
    return processPlcControlMode(refrigerationSystemId, controlMode, plcNodes);
    }

    /**
     * 查询制冷系统
     * @param systemId
     * @param controlMode
     * @return
     */
    private RefrigeratingSystem queryRefrigeratingSystem(Long systemId, ControlMode controlMode) {
    RefrigeratingSystem refrigeratingSystem = refrigeratingSystemDao.selectById(systemId);
    if (Objects.isNull(refrigeratingSystem)) {
        log.error("{}没有查到id为{}的制冷系统", RefrigerationConstantDef.AI_CONTROL_LOG, systemId);
    }
    return refrigeratingSystem;
    }

    /**
     * 处理plc设备的控制模式是否是ai
     * @param systemId
     * @param controlMode
     * @param plcNodes
     * @return
     */
    private Boolean processPlcControlMode(Long systemId, ControlMode controlMode, List<BaseVo> plcNodes) {
        List<MeasurePointData> dataList = queryRealTimeData(plcNodes, DeviceDataIdDef.PLC_DATAID_LIST);

        // 检查AI模式
        Optional<Double> aiValue = dataList.stream()
                .filter(data -> Objects.equals(data.getDataId(), QuantityDataIdEnum.PLC_AI_STATUS.getDataId()))
                .findFirst()
                .map(MeasurePointData::getValue);

        // 如果是ai模式返回true
        if (aiValue.filter(val -> MathUtils.equals(val, RefrigerationConstantDef.TRUE_DATA)).isPresent()) {
            return true;
        }
        // 不是ai模式，更新控制模式为plc模式
        updateToPlcMode(systemId, controlMode);

        return false;
    }

    /**
     * 更新控制模式为plc模式
     * @param systemId
     * @param controlMode
     */
    private void updateToPlcMode(Long systemId, ControlMode controlMode) {
        controlMode.setMode(ModelDef.PLC_AUTO);
        controlModeDao.updateControlMode(controlMode);
        log.warn("系统id为{}的控制模式调整为PLC", systemId);
        commonUtilsService.writeUpdateOperationLogs(65,
                systemId + "制冷系统的控制模式为plc",
                null,
                LoginDef.USER_ROOT
        );
    }
    // @AI-Generated-end

    /**
     * 查询实时数据
     * @param plcNodes
     * @param dataIdList
     * @return
     */
    private List<MeasurePointData> queryRealTimeData(List<BaseVo> plcNodes, List<Integer> dataIdList) {
        if (CollectionUtils.isEmpty(plcNodes)) {
            return new ArrayList<>();
        }
        List<MeasuredbyVo> measuredbyVos = measureByDao.queryMeasureBy(plcNodes);
        if (CollectionUtils.isEmpty(measuredbyVos)) {
            return new ArrayList<>();
        }
        // 查询测点
        List<Integer> deviceIds = measuredbyVos.stream().map(measuredbyVo -> measuredbyVo.getMeasuredby().intValue()).distinct().collect(Collectors.toList());
        List<MeasureNodeWithDeviceIdDTO> deviceIdDTOList = pecCoreService.queryMeasureNodeWithDeviceId(deviceIds);
        if (CollectionUtils.isEmpty(deviceIdDTOList)) {
            return new ArrayList<>();
        }
        // 找到目标测点
        List<DeviceDataIdLogicalId> deviceDataIdLogicalIdList = new ArrayList<>();
        deviceIdDTOList.forEach(deviceIdDTO -> {
            if (!dataIdList.contains(deviceIdDTO.getDataId())) {
                return;
            }
            DeviceDataIdLogicalId deviceDataIdLogicalId = new DeviceDataIdLogicalId();
            deviceDataIdLogicalId.setDeviceId(deviceIdDTO.getDeviceId());
            deviceDataIdLogicalId.setDataId(deviceIdDTO.getDataId());
            deviceDataIdLogicalId.setLogicalId(deviceIdDTO.getLogicalId());
            deviceDataIdLogicalIdList.add(deviceDataIdLogicalId);
        });
        // 查询实时数据
        ApiResult<List<MeasurePointData>> measurePointDataList = dataServiceRestApi.getMeasureDataByDevices(deviceDataIdLogicalIdList);
        if (!measurePointDataList.isSuccess()) {
            log.error("查询实时数据失败：{},查询的节点{}", measurePointDataList.getMsg(), JsonTransferUtils.toJSONString(plcNodes));
            return new ArrayList<>();
        }
        return measurePointDataList.getData();
    }

    /**
     * 判断当前时间是否在ai控制生效时间段内
     * @param controlMode
     * @return
     */
    private Boolean isEffectiveTime(ControlMode controlMode) {
        if (Objects.isNull(controlMode.getAiStartTime()) || Objects.isNull(controlMode.getAiEndTime())) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime aiStartTime = controlMode.getAiStartTime();
        LocalDateTime aiEndTime = controlMode.getAiEndTime();
        LocalDateTime  startTime = LocalDateTime.of(LocalDate.now(), aiStartTime.toLocalTime());
        LocalDateTime  endTime = LocalDateTime.of(LocalDate.now(), aiEndTime.toLocalTime());
        if (now.isAfter(startTime) && now.isBefore(endTime)) {
            return true;
        }
        return false;
    }
}
