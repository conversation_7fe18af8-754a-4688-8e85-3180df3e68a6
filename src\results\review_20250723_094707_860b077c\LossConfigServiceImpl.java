package com.cet.eem.fusion.loss.core.service.impl;

import com.cet.eem.fusion.common.configuration.CommonConfig;
import com.cet.eem.fusion.common.def.base.EnergyTypeDef;
import com.cet.eem.fusion.common.def.base.ExcelType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.common.ContentTypeDef;
import com.cet.eem.fusion.common.def.common.SplitCharDef;
import com.cet.eem.fusion.common.def.i18n.CommonLangKeyDef;
import com.cet.eem.fusion.common.def.i18n.EnumerationLangKeyDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.event.RedisEventKey;
import com.cet.eem.fusion.common.model.topology.po.LossGroupNodePOWithLayer;
import com.cet.eem.fusion.common.model.topology.po.LossNodeInfoPO;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.service.RedisService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ErrorUtils;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.eem.fusion.common.utils.time.TimeFormatDef;
import com.cet.eem.fusion.common.utils.time.TimeUtil;
import com.cet.eem.fusion.config.sdk.entity.EnergyTypeWithUnitDTO;
import com.cet.eem.fusion.config.sdk.entity.node.NodeTreeDTO;
import com.cet.eem.fusion.config.sdk.service.EemNodeService;
import com.cet.eem.fusion.config.sdk.service.ProjectEnergyTypeService;
import com.cet.eem.fusion.loss.core.config.LossCommonConfig;
import com.cet.eem.fusion.loss.core.dao.PowerConfigDao;
import com.cet.eem.fusion.loss.core.dao.topology.LossGroupNodePODao;
import com.cet.eem.fusion.loss.core.entity.vo.*;
import com.cet.eem.fusion.loss.core.handler.LossTreeMessageHandler;
import com.cet.eem.fusion.loss.core.model.PowerConfig;
import com.cet.eem.fusion.loss.core.service.LossConfigService;
import com.cet.eem.fusion.loss.core.service.LossGroupChangeService;
import com.cet.electric.baseconfig.common.base.BaseEntity;
import com.cet.electric.baseconfig.common.entity.PipeNetworkConnectionModel;
import com.cet.electric.baseconfig.sdk.entity.Node;
import com.cet.electric.baseconfig.sdk.entity.param.LabelAndId;
import com.cet.electric.baseconfig.sdk.service.NodeService;
import com.cet.electric.baseconfig.sdk.service.PipeNetworkConnectionService;
import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;
import com.cet.electric.model.enums.ConfirmEventStatusEnum;
import com.cet.electric.modelsdk.event.entity.dto.EventQueryDTO;
import com.cet.electric.modelsdk.event.model.SystemEvent;
import com.cet.electric.modelsdk.event.service.SystemEventService;
import com.cet.electric.modelservice.sdk.conditions.ListWithTotal;
import com.cet.futureblue.i18n.LanguageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cet.eem.fusion.common.def.i18n.EnergyLangKeyDef.CONSUMPTION.ENERGY_ENTRY_INFO_EXPORT_SERIAL_NUMBER;
import static com.cet.eem.fusion.common.def.i18n.PipeLangKeyDef.LossConfig.*;
import static com.cet.eem.fusion.loss.core.common.LossConstants.LINE_LOSS_EVENT_COL_WIDTH;

/**
 * <AUTHOR>
 * @description: 损耗配置实现类
 * @date 2025/5/27
 */
@Slf4j
@Service
public class LossConfigServiceImpl implements LossConfigService {


    @Resource
    LossTreeMessageHandler lossTreeMessageHandler;
    @Resource
    NodeService nodeService;
    @Resource
    PowerConfigDao powerConfigDao;
    @Resource
    private LossGroupNodePODao lossGroupNodePODao;
    @Resource
    LossCommonConfig lossCommonConfig;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private ProjectEnergyTypeService projectEnergyTypeService;
    @Resource
    RedisService redisService;
    @Resource
    ModelServiceUtils modelServiceUtils;
    @Resource
    PipeNetworkConnectionService pipeNetworkConnectionService;
    @Resource
    LossGroupChangeService lossGroupChangeService;
    @Resource
    SystemEventService systemEventService;
    @Resource
    EemNodeService eemNodeService;
    @Resource
    UserRestApi userService;

    @Override
    public List<LossTreeVo> getLossTree(LossConfigRequestVo lossConfigRequestVo, Boolean keepTransformer, Boolean keepEndEquipment, Long userId) {

        Integer energyType = lossConfigRequestVo.getEnergyType();
        Long tenantId = lossConfigRequestVo.getTenantId();
        List<NodeTreeDTO> nodeTreeDTOList = eemNodeService.queryRootNodeAuthFilter(tenantId, userId);
        if (CollectionUtils.isEmpty(nodeTreeDTOList)) {
            return new LinkedList<>();
        }
        List<LabelAndId> rootNodeList = nodeTreeDTOList.stream().map(nodeTreeDTO -> new LabelAndId(nodeTreeDTO.getModelLabel(), nodeTreeDTO.getId())).collect(Collectors.toList());
        List<Map> nodeInfoMap = nodeService.batchQueryInfoByLabelAndId(rootNodeList);
        if (CollectionUtils.isEmpty(nodeInfoMap)) {
            return Collections.emptyList();
        }
        Map<LabelAndId, String> rootNodeInfo = new HashMap<>(nodeInfoMap.size());
        for (Map map : nodeInfoMap) {
            rootNodeInfo.put(new LabelAndId((String) map.get(ColumnDef.MODEL_LABEL),
                    Long.valueOf(map.get(ColumnDef.ID).toString())), map.get(ColumnDef.NAME).toString());
        }
        List<LossTreeVo> lossTreeVoList = new LinkedList<>();
        for (LabelAndId root : rootNodeList) {
            List<LossTreeVo> tree = getLossLevelNodeTree(energyType, root, keepTransformer, keepEndEquipment,
                    true, lossConfigRequestVo.getKeepLossGroup(), null);
            String name = rootNodeInfo.get(root);
            if (StringUtils.isBlank(name)) {
                log.error("项目根节点为空");
                continue;
            }
            LossTreeVo projectNode = new LossTreeVo();
            projectNode.setTreeId(root.getModelLabel() + SplitCharDef.UNDERLINE + root.getId());
            projectNode.setChildren(tree);
            projectNode.setId(root.getId());
            projectNode.setModelLabel(root.getModelLabel());
            projectNode.setName(name);
            log.debug("损耗节点树：{}", JsonTransferUtils.toJSONString(projectNode));
            lossTreeVoList.add(projectNode);
        }
        return lossTreeVoList;
    }

    @Override
    public void lossConfigWithLossGroup(LineLossRateConfigVo configVo) {
        PowerConfig powerConfig = powerConfigDao.queryPowerConfig();
        List<LineLossRateConfigVo> config = new ArrayList<>();
        if (Objects.nonNull(powerConfig)) {
            config = powerConfig.getConfig();
        }
        Map<BaseVo, List<BaseVo>> lossGroup = getLossGroup();
        powerConfigProcessWithLossGroup(config, configVo, lossGroup);
        powerConfigDao.writePowerConfig(powerConfig, config);
    }

    @Override
    public void batchSetLossRateConfig(List<LineLossRateConfigVo> configVoList) {
        if (CollectionUtils.isEmpty(configVoList)) {
            return;
        }
        PowerConfig powerConfig = powerConfigDao.queryPowerConfig();
        List<LineLossRateConfigVo> config = new ArrayList<>();
        if (Objects.nonNull(powerConfig)) {
            config = powerConfig.getConfig();
        }
        // powerConfig为null
        if (CollectionUtils.isEmpty(config)) {
            powerConfig = new PowerConfig();
        }
        List<LineLossRateConfigVo> finalConfig = config;
        Map<LineLossRateConfigVo, LineLossRateConfigVo> baseVoListMap = config.stream()
                .collect(Collectors.toMap(Function.identity(), Function.identity()));
        for (LineLossRateConfigVo lineLossRateConfigVo : configVoList) {
            LineLossRateConfigVo rateConfigVo = baseVoListMap.get(lineLossRateConfigVo);
            if (Objects.nonNull(rateConfigVo)) {
                rateConfigVo.setValue(lineLossRateConfigVo.getValue());
            }
        }
        configVoList.forEach(node -> powerConfigProcess(finalConfig, node));
        powerConfigDao.writePowerConfig(powerConfig, finalConfig);
    }

    @Override
    public List<Node> queryStartNodes(Integer energyType, Long tenantId, Long userId) {
        List<NodeTreeDTO> rootNodeList = eemNodeService.queryRootNodeAuthFilter(tenantId, userId);
        List<PipeNetworkConnectionModel> networkConnectionModelList = new ArrayList<>();
        if (CollectionUtils.isEmpty(rootNodeList)) {
            return Collections.emptyList();
        }
        for (NodeTreeDTO nodeTreeDTO : rootNodeList) {
            networkConnectionModelList.addAll(pipeNetworkConnectionService.queryPipeNetworkConnectBatch(nodeTreeDTO.getModelLabel(),
                    nodeTreeDTO.getId(), energyType));
        }

        Set<BaseVo> allNodes = new HashSet<>();
        List<BaseVo> headerNodeList = new ArrayList<>();
        // 收集所有节点并建立邻接表
        for (PipeNetworkConnectionModel connection : networkConnectionModelList) {
            Long sourceId = connection.getInflowId();
            String sourceLabel = connection.getInflowLabel();
            Long targetId = connection.getOutFlowId();
            String targetLabel = connection.getOutFlowLabel();
            BaseVo sourceNode = new BaseVo(sourceLabel, sourceId);
            BaseVo targetNode = new BaseVo(targetLabel, targetId);

            allNodes.add(sourceNode);
            allNodes.add(targetNode);
        }

        // 找出没有入度的头结点
        for (BaseVo node : allNodes) {
            boolean isHeadNode = networkConnectionModelList.stream()
                    .noneMatch(conn -> Objects.equals(node.getId(), conn.getOutFlowId())
                            && Objects.equals(node.getModelLabel(), conn.getOutFlowLabel()));  // 检查是否不存在作为目标节点的连接

            if (isHeadNode) {
                headerNodeList.add(node);
            }
        }
        Map<BaseVo, List<BaseVo>> lossGroupMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        rootNodeList.forEach(node -> lossGroupChangeService.getLossGroup(new LabelAndId(node.getModelLabel(), node.getId())));
        Set<BaseVo> changeNodes = lossGroupChangeService.getChangeNodeByLossGroup(headerNodeList, lossGroupMap);
        if (CollectionUtils.isEmpty(changeNodes)) {
            return Collections.emptyList();
        }
        List<LabelAndId> labelAndIdList = changeNodes.stream().filter(baseVo -> Objects.isNull(baseVo.getName())).map(baseVo -> new LabelAndId(baseVo.getModelLabel(), baseVo.getId())).collect(Collectors.toList());
        List<Node> nameByLabelAndId = nodeService.batchQueryNameByLabelAndId(labelAndIdList);
        Map<BaseVo, Node> baseVoMap = nameByLabelAndId.stream().collect(Collectors.toMap(node -> new BaseVo(node.getId(), node.getModelLabel()), Function.identity()));
        List<Node> result = new ArrayList<>();
        for (BaseVo node : changeNodes) {
            if (Objects.nonNull(node.getName())) {
                result.add(new Node(node.getId(), node.getModelLabel(), node.getName()));
                continue;
            }
            Node tempNode = baseVoMap.get(node);
            if (Objects.nonNull(tempNode)) {
                result.add(new Node(tempNode.getId(), tempNode.getModelLabel(), tempNode.getName()));
            }
        }
        return sortNodes(result);
    }


    /**
     * 节点按modelLabel和id排序
     *
     * @param baseVos
     * @return
     */
    private List<Node> sortNodes(List<Node> baseVos) {
        if (CollectionUtils.isEmpty(baseVos)) {
            return baseVos;
        }
        return baseVos.stream().sorted(Comparator.comparing(Node::getModelLabel)
                .thenComparing(Node::getId)).collect(Collectors.toList());
    }


    private void powerConfigProcess(List<LineLossRateConfigVo> finalConfig, LineLossRateConfigVo node) {
        Optional<LineLossRateConfigVo> optional = finalConfig.stream()
                .filter(it -> Objects.equals(it.getModelLabel(), node.getModelLabel())
                        && Objects.equals(it.getId(), node.getId()))
                .findFirst();
        if (optional.isPresent()) {
            optional.get().setValue(node.getValue());
        } else {
            finalConfig.add(node);
        }
    }


    @Override
    public Map<BaseVo, List<BaseVo>> getLossGroup() {
        List<LossGroupNodePOWithLayer> lossGroup = lossGroupNodePODao.queryAll();
        if (CollectionUtils.isEmpty(lossGroup)) {
            return Collections.emptyMap();
        }
        Map<BaseVo, List<BaseVo>> treeNodeListMap = new HashMap<>();
        for (LossGroupNodePOWithLayer lossGroupNodePOWithLayer : lossGroup) {
            List<LossNodeInfoPO> lossNodeInfoPOList = lossGroupNodePOWithLayer.getLossNodeInfoPOList();
            if (CollectionUtils.isEmpty(lossNodeInfoPOList)) {
                continue;
            }
            List<BaseVo> childList = lossNodeInfoPOList.stream().map(it -> new BaseVo(it.getId(), it.getModelLabel()))
                    .collect(Collectors.toList());
            treeNodeListMap.put(new BaseVo(lossGroupNodePOWithLayer.getId(), lossGroupNodePOWithLayer.getModelLabel()), childList);
        }
        return treeNodeListMap;
    }

    @Override
    public ListWithTotal<SystemEventVo> getSystemEvents(SystemEventRequestVo requestVo, Long userId) {
        LossConfigRequestVo lossConfigRequestVo = new LossConfigRequestVo();
        lossConfigRequestVo.setEnergyType(requestVo.getEnergyType());
        lossConfigRequestVo.setTenantId(requestVo.getTenantId());
        List<LossTreeVo> lossTreeVoList = getLossTree(lossConfigRequestVo, requestVo.getKeepTransformer(), false, userId);
        List<LossTreeVo> nodesList = getNodesByLossTree(lossTreeVoList, requestVo.getNodes(), false);
        if (CollectionUtils.isEmpty(nodesList)) {
            return ListWithTotal.empty();
        }
        EventQueryDTO lossEventQueryDTO = genLossEventQueryParam(nodesList, false, requestVo);
        ListWithTotal<SystemEvent> systemEventListWithTotal = systemEventService.querySystemEventList(lossEventQueryDTO);
        List<SystemEvent> systemEventList = systemEventListWithTotal.getList();

        if (CollectionUtils.isEmpty(systemEventList)) {
            return ListWithTotal.empty();
        }
        List<Long> userIdList = systemEventList.stream().map(SystemEvent::getOperatorId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, UserVo> userVoMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        if (CollectionUtils.isNotEmpty(userIdList)) {
            ApiResultI18n<List<UserVo>> userQueryResult = userService.getUsers(userIdList);
            List<UserVo> userVoList = userQueryResult.getData();
            userVoMap = userVoList.stream().collect(Collectors.toMap(UserVo::getId, Function.identity(), (v1, v2) -> v1));
        }
        List<SystemEventVo> systemEventVoList = genSystemEventVos(systemEventList, nodesList, userVoMap);
        return new ListWithTotal<>(systemEventListWithTotal.getTotal(), systemEventVoList);
    }


    /**
     * 获取请求的入参
     *
     * @param nodes
     * @param timeAsc
     * @param requestVo
     * @return
     */
    private EventQueryDTO genLossEventQueryParam(List<LossTreeVo> nodes, Boolean timeAsc, SystemEventRequestVo requestVo) {
        List<BaseEntity> nodeEntity = nodes.stream().map(node -> new BaseEntity(node.getId(), node.getModelLabel())).collect(Collectors.toList());
        return EventQueryDTO.builder()
                .nodes(nodeEntity)
                .timeAsc(timeAsc)
                .starTime(requestVo.getStartTime())
                .endTime(requestVo.getEndTime())
                .energyType(requestVo.getEnergyType())
                .eventType(requestVo.getEventType())
                .cycle(requestVo.getCycle())
                .lossLevel(requestVo.getLossLevel())
                .status(requestVo.getStatus())
                .index(requestVo.getIndex())
                .limit(requestVo.getLimit())
                .build();
    }

    private List<LossTreeVo> getNodesByLossTree(List<LossTreeVo> tree, List<BaseVo> nodes, boolean contain) {
        if (CollectionUtils.isEmpty(tree) || CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }
        return tree.stream().map(node -> {
            boolean newContain = contain || nodes.stream().anyMatch(item -> Objects.equals(item.getModelLabel(), node.getModelLabel()) && Objects.equals(item.getId(), node.getId()));
            List<LossTreeVo> children = getNodesByLossTree(node.getChildren(), nodes, newContain);
            if (newContain) {
                return Stream.of(Collections.singletonList(node), children).flatMap(Collection::stream).collect(Collectors.toList());
            }
            return children;
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).distinct().collect(Collectors.toList());

    }

    @Override
    public void exportSystemEvents(SystemEventRequestVo requestVo, Long userId, HttpServletResponse response) {

        ListWithTotal<SystemEventVo> systemEventVoListWithTotal = getSystemEvents(requestVo, userId);
        List<SystemEventVo> systemEventVoList = systemEventVoListWithTotal.getList();
        if (systemEventVoList.size() > lossCommonConfig.getExportMaxCount()) {
            systemEventVoList = systemEventVoList.stream().limit(lossCommonConfig.getExportMaxCount()).collect(Collectors.toList());
        }
        String excelName = LanguageUtil.getMessage(LOSS_ALARM_DATA);
        List<String> headerList = Arrays.asList(LanguageUtil.getMessage(ENERGY_ENTRY_INFO_EXPORT_SERIAL_NUMBER), LanguageUtil.getMessage(TIME), LanguageUtil.getMessage(ENERGY_TYPE),
                LanguageUtil.getMessage(ALARM_TYPE), LanguageUtil.getMessage(LOSS_LEVEL),
                LanguageUtil.getMessage(CommonLangKeyDef.GlobalInfo.INFO_ROOM), LanguageUtil.getMessage(CommonLangKeyDef.GlobalInfo.INFO_EQUIPMENT), LanguageUtil.getMessage(DESC), LanguageUtil.getMessage(STATUS));
        List<List<String>> values = genSystemEventValue(systemEventVoList, requestVo.getTenantId());

        try (Workbook workbook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA)) {
            String workbookName = excelName + "(" + TimeUtil.format(requestVo.getStartTime(), TimeFormatDef.DAY)
                    + "~" + TimeUtil.format(requestVo.getEndTime(), TimeFormatDef.DAY) + ")" + ".xlsx";

            PoiExcelUtils.createSheet(workbook, excelName, (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;
                PoiExcelUtils.createHeaderName(sheet, rowNum, headerList, baseCellStyle);
                for (int i = 0; i < headerList.size(); i++) {
                    sheet.setDefaultColumnStyle(i, baseCellStyle);
                }
                int col;
                // 时间	能源类型	报警类型	损耗级别	房间	设备	描述	状态
                int num = 1;
                for (List<String> value : values) {
                    Row row = PoiExcelUtils.createRow(sheet, ++rowNum);
                    col = 0;
                    PoiExcelUtils.createCell(row, col++, baseCellStyle, num++);
                    for (String content : value) {
                        PoiExcelUtils.createCell(row, col++, baseCellStyle, content);

                    }
                }
            }, LINE_LOSS_EVENT_COL_WIDTH);

            FileUtils.downloadExcel(response, workbook, workbookName, ContentTypeDef.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            ErrorUtils.exportError(excelName, e);
        }
    }

    private List<List<String>> genSystemEventValue(List<SystemEventVo> systemEvents, Long tenantId) {
        if (CollectionUtils.isEmpty(systemEvents)) {
            return Collections.emptyList();
        }
        List<Integer> energyTypeList = systemEvents.stream().map(SystemEventVo::getEnergyType).distinct().collect(Collectors.toList());
        List<EnergyTypeWithUnitDTO> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(tenantId, energyTypeList);
        Map<Integer, EnergyTypeWithUnitDTO> energyTypeMap = projectEnergyTypes.stream().collect(Collectors.toMap(EnergyTypeWithUnitDTO::getEnergyType, Function.identity(), (v1, v2) -> v1));
        return systemEvents.stream().map(item -> {
            String logTimeStr = TimeUtil.format(item.getLogTime(), TimeFormatDef.LONG_TIME_FORMAT);
            String energyTypeStr = energyTypeMap.get(item.getEnergyType()).getName();
            String cycleStr = getEventTypeStrByCycle(item.getCycle());
            Integer lossLevel = item.getLossLevel();
            // 变压器没有识别层数，固定0
            if (Objects.isNull(lossLevel)) {
                lossLevel = 0;
            }
            String lossLevelStr = LanguageUtil.getMessage(DEFAULT, lossLevel + 1);
            String roomName = item.getRoomName();
            String equipmentName = item.getObjectName();
            String desc = item.getDescription();
            String status = null;
            switch (item.getStatus()) {
                case 1:
                    status = LanguageUtil.getMessage(EnumerationLangKeyDef.PENDING);
                    break;
                case 3:
                    status = LanguageUtil.getMessage(EnumerationLangKeyDef.PROCESSED);
                    break;
                default:
            }
            return Arrays.asList(logTimeStr, energyTypeStr, cycleStr, lossLevelStr, roomName, equipmentName, desc, status);
        }).collect(Collectors.toList());

    }

    private String getEventTypeStrByCycle(Integer cycle) {
        switch (cycle) {
            case 7:
                return LanguageUtil.getMessage(HOUR);
            case 12:
                return LanguageUtil.getMessage(DAY);
            default:
                return LanguageUtil.getMessage(NOT_EXIST);
        }
    }


    @Override
    public void updateLossEvent(EventHandleVo eventHandleVo) {
        List<String> writeDataFields =
                Arrays.asList(ColumnDef.REMARK, ColumnDef.CONFIRM_EVENT_STATUS, ColumnDef.OPERATOR, ColumnDef.OPERATOR_ID);
        List<List<Object>> writeDataList = new ArrayList<>();
        writeDataList.add(Arrays.asList(eventHandleVo.getRemark(), eventHandleVo.getStatus(), eventHandleVo.getOperatorName(), eventHandleVo.getOperatorId()));

        modelServiceUtils.updateDataBatchByIds(ModelLabelDef.SYSTEM_EVENT,
                writeDataFields, writeDataList, Collections.singletonList(Collections.singletonList(eventHandleVo.getEventId())));
        // 写到缓存里
        List<SystemEvent> list = systemEventService.querySystemEventList(Collections.singletonList(eventHandleVo.getEventId()));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SystemEvent systemEvent = list.get(0);

        writeToRedis(systemEvent);
    }

    private void writeToRedis(SystemEvent event) {
        String key = event.getObjectId() + SplitCharDef.UNDERLINE + event.getObjectLabel() + SplitCharDef.UNDERLINE +
                event.getEventTime() + SplitCharDef.UNDERLINE + event.getEventType() + SplitCharDef.UNDERLINE + event.getLevel();
        redisService.stringAddValue(RedisEventKey.EVENT_KEY_PREFIX + key,
                JsonTransferUtils.toJSONString(ConfirmEventStatusEnum.CONFIRMED.getCode()), 1, TimeUnit.DAYS);
    }

    private List<SystemEventVo> genSystemEventVos(List<SystemEvent> systemEvents, List<LossTreeVo> lossObjectVos, Map<Long, UserVo> userVoMap) {
        if (CollectionUtils.isEmpty(systemEvents)) {
            return Collections.emptyList();
        }
        Map<String, LossTreeVo> lossObjectVoMap = lossObjectVos.stream().collect(Collectors.toMap(item -> item.getModelLabel() + "_" + item.getId(), item -> item));
        return systemEvents.stream().map(event -> {
            String key = event.getObjectLabel() + SplitCharDef.UNDERLINE + event.getObjectId();
            LossTreeVo lossObjectVo = lossObjectVoMap.getOrDefault(key, new LossTreeVo());
            String statusName = Arrays.stream(ConfirmEventStatusEnum.values())
                    .filter(item -> Objects.equals(item.getCode(), event.getConfirmEventStatus()))
                    .findFirst().map(ConfirmEventStatusEnum::getMessage).orElse(null);
            Integer lossLevel = Objects.isNull(event.getExtend()) ? null : event.getExtend().getLossLevel();
            Integer cycle = Objects.isNull(event.getExtend()) ? null : event.getExtend().getCycle();
            UserVo userVo = userVoMap.get(event.getOperatorId());
            String userName = null;
            if (Objects.nonNull(userVo)) {
                String nicName = userVo.getNicName();
                String name = userVo.getName();
                userName = userVo.getNicName() + "(" + name + ")";
                if (StringUtils.isBlank(nicName)) {
                    userName = name;
                }
            }
            return SystemEventVo.builder()
                    .id(event.getId())
                    .lossLevel(lossLevel)
                    .objectId(event.getObjectId())
                    .objectLabel(event.getObjectLabel())
                    .eventType(event.getEventType())
                    .energyType(event.getEnergyType())
                    .description(event.getDescription())
                    .roomId(lossObjectVo.getRoomId())
                    .roomName(lossObjectVo.getRoomName())
                    .objectName(lossObjectVo.getName())
                    .description(event.getDescription())
                    .logTime(event.getEventTime())
                    .status(event.getConfirmEventStatus())
                    .statusName(statusName)
                    .cycle(cycle)
                    .operatorId(event.getOperatorId())
                    .operatorName(userName)
                    .remark(event.getRemark())
                    .extend(event.getExtend())
                    .build();
        }).collect(Collectors.toList());

    }

    @Override
    public List<LossTreeVo> getLossLevelNodeTree(Integer energyType, LabelAndId rootNode,
                                                 Boolean keepTransformer,
                                                 Boolean keepEndEquipment,
                                                 Boolean queryLineSegment,
                                                 Boolean keepLossGroup, List<BaseVo> startNodes) {
        if (Objects.isNull(keepTransformer)) {
            keepTransformer = true;
        }
        if (Objects.isNull(keepEndEquipment)) {
            keepEndEquipment = false;
        }
        if (Objects.isNull(queryLineSegment)) {
            queryLineSegment = true;
        }

        if (Objects.equals(EnergyTypeDef.ELECTRIC, energyType)) {
            lossTreeMessageHandler.setReservedModelList(NodeLabelDef.TOPOLOGY_ANALYSIS_RESERVE_DEVICES);
        } else {
            lossTreeMessageHandler.setReservedModelList(new ArrayList<>());
        }
        lossTreeMessageHandler.setKeepEndEquipment(keepEndEquipment);
        lossTreeMessageHandler.setKeepTransformer(keepTransformer);
        lossTreeMessageHandler.setQueryLineSegment(queryLineSegment);
        lossTreeMessageHandler.setKeepLossGroup(keepLossGroup);

        return lossTreeMessageHandler.getLossTree(rootNode, energyType, startNodes);
    }

    @Override
    public Map<String, Object> getRelationProperties() {
        List<Integer> standardEnergyTypeList = commonConfig.getStandardEnergyTypeList();
        boolean hideOverViewStatistics = lossCommonConfig.isHideOverViewStatistics();
        List<Integer> alarmAggregationType = lossCommonConfig.getAlarmAggregationType();
        Integer compareNodeLimit = lossCommonConfig.getCompareNodeLimit();
        Integer exportMaxCount = lossCommonConfig.getExportMaxCount();
        // 容积等于配置数量
        HashMap<String, Object> propertiesMap = new HashMap<>(4);
        propertiesMap.put("standardEnergyType", standardEnergyTypeList);
        propertiesMap.put("hideOverViewStatistics", hideOverViewStatistics);
        propertiesMap.put("alarmAggregationType", alarmAggregationType);
        propertiesMap.put("compareNodeLimit", compareNodeLimit);
        propertiesMap.put("exportMaxCount", exportMaxCount);
        return propertiesMap;
    }

    /**
     * 拼接写入的数据
     *
     * @param finalConfig
     * @param node
     * @param lossGroup
     */
    private void powerConfigProcessWithLossGroup(List<LineLossRateConfigVo> finalConfig, LineLossRateConfigVo node,
                                                 Map<BaseVo, List<BaseVo>> lossGroup) {
        List<BaseVo> treeNodes = lossGroup.get(new BaseVo(node.getId(), node.getModelLabel()));

        if (CollectionUtils.isEmpty(treeNodes)) {
            assembleLossConfigData(finalConfig, node.getId(), node.getModelLabel(), node.getValue());
        } else {
            for (BaseVo treeNode : treeNodes) {
                assembleLossConfigData(finalConfig, treeNode.getId(), treeNode.getModelLabel(), node.getValue());
            }
        }
    }

    /**
     * 拼接数据
     *
     * @param oldConfig
     * @param objectId
     * @param objectLabel
     * @param value
     */
    private void assembleLossConfigData(List<LineLossRateConfigVo> oldConfig, Long objectId, String objectLabel, Double value) {
        Optional<LineLossRateConfigVo> optional = oldConfig.stream()
                .filter(it -> Objects.equals(it.getModelLabel(), objectLabel)
                        && Objects.equals(it.getId(), objectId))
                .findFirst();
        if (optional.isPresent()) {
            optional.get().setValue(value);
        } else {
            oldConfig.add(new LineLossRateConfigVo(objectId, objectLabel, value));
        }
    }
}
