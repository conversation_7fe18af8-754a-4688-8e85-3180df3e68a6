# Vue 3 + Element Plus + Axios 示例项目

这是一个使用 Vue 3、TypeScript、Vite、Element Plus 和 Axios 构建的示例项目。

## 技术栈

- [Vue 3](https://v3.vuejs.org/) - 渐进式 JavaScript 框架
- [TypeScript](https://www.typescriptlang.org/) - JavaScript 的超集，添加了类型系统
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Element Plus](https://element-plus.org/) - 基于 Vue 3 的组件库
- [Axios](https://axios-http.com/) - 基于 Promise 的 HTTP 客户端

## 功能特性

- 使用 Vue 3 的 Composition API
- 使用 TypeScript 进行类型检查
- 集成 Element Plus UI 组件库
- 使用 Axios 进行 API 请求
- 配置 API 请求代理
- 示例 API 服务和组件

## 项目结构

```
├── public/              # 静态资源
├── src/                 # 源代码
│   ├── api/             # API 服务
│   ├── assets/          # 资源文件
│   ├── components/      # 组件
│   ├── App.vue          # 根组件
│   ├── main.ts          # 入口文件
│   └── style.css        # 全局样式
├── index.html           # HTML 模板
├── package.json         # 项目依赖
├── tsconfig.json        # TypeScript 配置
├── vite.config.ts       # Vite 配置
└── README.md            # 项目说明
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## API 示例

项目使用 [JSONPlaceholder](https://jsonplaceholder.typicode.com/) 作为示例 API。在 `src/api/index.ts` 文件中配置了 Axios 实例和拦截器，并提供了一些示例 API 方法。

## Element Plus 组件示例

在 `src/components/ApiDemo.vue` 组件中展示了如何使用 Element Plus 组件和 Axios 进行 API 请求。

## 开发指南

1. 在 `src/api` 目录下添加新的 API 服务
2. 在 `src/components` 目录下创建新的组件
3. 在 `src/App.vue` 中引入和使用组件

## 许可证

[MIT](LICENSE)
