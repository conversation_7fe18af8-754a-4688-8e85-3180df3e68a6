{"total_problems": 43, "problems": [{"problem_id": 1, "location": "AiPlcControlServiceV2Impl.java:executeStartOrStopControl方法", "description": "在循环或频繁调用中使用Thread.sleep可能导致性能问题或资源浪费", "severity": "中", "suggestion": "使用ScheduledExecutorService替代Thread.sleep进行定时任务调度"}, {"problem_id": 2, "location": "AiPlcControlServiceV2Impl.java:singleTeleAdjustingControl方法", "description": "在循环或频繁调用中使用Thread.sleep可能导致性能问题或资源浪费", "severity": "中", "suggestion": "使用ScheduledExecutorService替代Thread.sleep进行定时任务调度"}, {"problem_id": 3, "location": "AiPlcControlServiceV2Impl.java:processControlParameters方法", "description": "在循环中调用数据库查询（handleCoolingWaterTemp）可能导致多次数据库操作", "severity": "高", "suggestion": "将数据库查询移出循环，在循环外部统一处理"}, {"problem_id": 4, "location": "AiPlcControlServiceV2Impl.java:queryDeviceAndControlPoints方法", "description": "多次数据库查询可能导致性能瓶颈", "severity": "高", "suggestion": "合并或优化数据库查询，减少数据库访问次数"}, {"problem_id": 5, "location": "AiPlcControlServiceV2Impl.java:queryDeviceAndControlPoints方法", "description": "频繁调用外部服务接口可能导致性能下降", "severity": "高", "suggestion": "合并外部服务调用，减少调用次数"}, {"problem_id": 6, "location": "AiPlcControlServiceV2Impl.java:executeTeleAdjustingControl方法", "description": "循环内执行单个操作可能导致性能问题", "severity": "中", "suggestion": "批量处理遥调命令，减少循环次数"}, {"problem_id": 7, "location": "AiPlcControlServiceV2Impl.java:queryRealTimeData方法", "description": "多次数据库查询可能导致性能问题", "severity": "高", "suggestion": "合并数据库查询，使用缓存优化"}, {"problem_id": 8, "location": "AiPlcControlServiceV2Impl.java:isEffectiveTime方法", "description": "频繁计算时间可能导致性能开销", "severity": "低", "suggestion": "避免在频繁调用的方法中重复计算时间，考虑缓存结果"}, {"problem_id": 9, "location": "AiPlcControlServiceV2Impl.java:createRemoteControlIdParam方法", "description": "在循环中频繁操作Map可能导致性能问题", "severity": "低", "suggestion": "优化Map操作，减少循环内操作"}, {"problem_id": 10, "location": "AiPlcControlServiceV2Impl.java:181", "description": "密码以明文形式存储和使用，违反了敏感信息保护原则", "severity": "高", "suggestion": "避免明文存储和传输密码，应使用加密机制保护敏感信息"}, {"problem_id": 11, "location": "AiPlcControlServiceV2Impl.java:181", "description": "密码以明文形式获取，未加密传输或存储", "severity": "高", "suggestion": "使用安全的密码管理机制，避免明文密码在系统中流转"}, {"problem_id": 12, "location": "AiPlcControlServiceV2Impl.java:182", "description": "在用户对象中直接设置明文密码", "severity": "高", "suggestion": "避免在用户对象中存储明文密码，应使用加密机制"}, {"problem_id": 13, "location": "AiPlcControlServiceV2Impl.java:181", "description": "明文密码可能被日志记录或泄露", "severity": "高", "suggestion": "确保密码不在日志中记录，避免密码泄露"}, {"problem_id": 14, "location": "AiPlcControlServiceV2Impl.java:219", "description": "在用户对象中设置明文密码", "severity": "高", "suggestion": "避免在用户对象中存储明文密码，应使用加密机制"}, {"problem_id": 15, "location": "AiPlcControlServiceV2Impl.java:221", "description": "使用MD5进行密码摘要，MD5不安全", "severity": "高", "suggestion": "使用更安全的哈希算法（如SHA-256）并加盐处理"}, {"problem_id": 16, "location": "AiPlcControlServiceV2Impl.java:224", "description": "使用MD5进行密码摘要，MD5不安全", "severity": "高", "suggestion": "使用更安全的哈希算法（如SHA-256）并加盐处理"}, {"problem_id": 17, "location": "AiPlcControlServiceV2Impl.java:233", "description": "硬编码等待时间，可能导致线程阻塞", "severity": "中", "suggestion": "使用异步任务或定时任务代替线程休眠"}, {"problem_id": 18, "location": "AiPlcControlServiceV2Impl.java:269", "description": "硬编码等待时间，可能导致线程阻塞", "severity": "中", "suggestion": "使用异步任务或定时任务代替线程休眠"}, {"problem_id": 19, "location": "AiPlcControlServiceV2Impl.java:285", "description": "使用MD5进行密码摘要，MD5不安全", "severity": "高", "suggestion": "使用更安全的哈希算法（如SHA-256）并加盐处理"}, {"problem_id": 20, "location": "AiPlcControlServiceV2Impl.java:286", "description": "使用MD5摘要进行身份验证，存在安全风险", "severity": "高", "suggestion": "使用更安全的认证机制，如BCrypt或PBKDF2"}, {"problem_id": 21, "location": "AiPlcControlServiceV2Impl.java:288", "description": "未对敏感操作进行审计日志记录", "severity": "中", "suggestion": "对敏感操作进行详细的审计日志记录"}, {"problem_id": 22, "location": "AiPlcControlServiceV2Impl.java:290", "description": "日志记录敏感信息（如遥控参数）", "severity": "高", "suggestion": "避免在日志中记录敏感信息"}, {"problem_id": 23, "location": "AiPlcControlServiceV2Impl.java:295", "description": "日志记录敏感信息（如遥控参数）", "severity": "高", "suggestion": "避免在日志中记录敏感信息"}, {"problem_id": 24, "location": "AiPlcControlServiceV2Impl.java:440", "description": "获取明文密码", "severity": "高", "suggestion": "避免获取明文密码，应使用加密机制"}, {"problem_id": 25, "location": "AiPlcControlServiceV2Impl.java:443", "description": "使用MD5进行密码摘要，MD5不安全", "severity": "高", "suggestion": "使用更安全的哈希算法（如SHA-256）并加盐处理"}, {"problem_id": 26, "location": "AiPlcControlServiceV2Impl.java:449", "description": "获取明文密码", "severity": "高", "suggestion": "避免获取明文密码，应使用加密机制"}, {"problem_id": 27, "location": "AiPlcControlServiceV2Impl.java:477", "description": "在用户对象中设置明文密码", "severity": "高", "suggestion": "避免在用户对象中存储明文密码，应使用加密机制"}, {"problem_id": 28, "location": "AiPlcControlServiceV2Impl.java:510", "description": "在用户对象中设置明文密码", "severity": "高"}]}