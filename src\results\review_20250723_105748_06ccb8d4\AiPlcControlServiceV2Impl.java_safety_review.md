Assistant: Java安全代码扫描-AiPlcControlServiceV2Impl.java
| 问题编号 | 问题代码行 | 问题代码 | 问题描述 | 问题类别 | 问题级别 | 修改建议 |
|----------|------------|----------|----------|----------|----------|----------|
| 1 | 第181行 | `String password = authUtils.queryUser(LoginDef.USER_ROOT).getPassword();` 等 | 密码以明文形式存储和使用，违反了敏感信息保护原则 | 强制 | 高 | 避免明文存储和传输密码，应使用加密机制保护敏感信息 |
| 2 | 第181行 | `String password = getPassword(userVo);` | 密码以明文形式获取，未加密传输或存储 | 强制 | 高 | 使用安全的密码管理机制，避免明文密码在系统中流转 |
| 3 | 第182行 | `userVo.setPassword(password);` | 在用户对象中直接设置明文密码 | 强制 | 高 | 避免在用户对象中存储明文密码，应使用加密机制 |
| 4 | 第181行 | `String password = getPassword(userVo);` | 明文密码可能被日志记录或泄露 | 强制 | 高 | 确保密码不在日志中记录，避免密码泄露 |
| 5 | 第219行 | `userVo.setPassword(password);` | 在用户对象中设置明文密码 | 强制 | 高 | 避免在用户对象中存储明文密码，应使用加密机制 |
| 6 | 第221行 | `MultiRemoteControlPara remoteControlPara = createControlParam(paramIds, userVo);` | 使用MD5进行密码摘要，MD5不安全 | 强制 | 高 | 使用更安全的哈希算法（如SHA-256）并加盐处理 |
| 7 | 第224行 | `String md5Hex = DigestUtils.md5DigestAsHex(...)` | 使用MD5进行密码摘要，MD5不安全 | 强制 | 高 | 使用更安全的哈希算法（如SHA-256）并加盐处理 |
| 8 | 第233行 | `Thread.sleep(interval * TimeUtil.SECOND);` | 硬编码等待时间，可能导致线程阻塞 | 建议 | 中 | 使用异步任务或定时任务代替线程休眠 |
| 9 | 第269行 | `Thread.sleep(aiScheduleConfig.getRetryWait() * TimeUtil.SECOND);` | 硬编码等待时间，可能导致线程阻塞 | 建议 | 中 | 使用异步任务或定时任务代替线程休眠 |
| 10 | 第285行 | `String md5Hex = DigestUtils.md5DigestAsHex(...)` | 使用MD5进行密码摘要，MD5不安全 | 强制 | 高 | 使用更安全的哈希算法（如SHA-256）并加盐处理 |
| 11 | 第286行 | `UserPasswordParam userInfo = new UserPasswordParam(userVo.getName(), md5Hex, WEB, now);` | 使用MD5摘要进行身份验证，存在安全风险 | 强制 | 高 | 使用更安全的认证机制，如BCrypt或PBKDF2 |
| 12 | 第288行 | `dataServiceRestApi.remoteControlByParamId(singleParam, true, null);` | 未对敏感操作进行审计日志记录 | 建议 | 中 | 对敏感操作进行详细的审计日志记录 |
| 13 | 第290行 | `log.info("{}遥调成功, 接口入参：{}，接口返回信息：{}", ..., JsonTransferUtils.toJSONString(singleParam), ...);` | 日志记录敏感信息（如遥控参数） | 强制 | 高 | 避免在日志中记录敏感信息 |
| 14 | 第295行 | `log.info("{}遥调失败，接口入参：{}，接口返回信息：{}", ..., JsonTransferUtils.toJSONString(singleParam), ...);` | 日志记录敏感信息（如遥控参数） | 强制 | 高 | 避免在日志中记录敏感信息 |
| 15 | 第440行 | `String password = authUtils.getPassword(userVo.getId());` | 获取明文密码 | 强制 | 高 | 避免获取明文密码，应使用加密机制 |
| 16 | 第443行 | `String md5Hex = DigestUtils.md5DigestAsHex(...)` | 使用MD5进行密码摘要，MD5不安全 | 强制 | 高 | 使用更安全的哈希算法（如SHA-256）并加盐处理 |
| 17 | 第449行 | `return authUtils.getDecryptPassword(queryUserPassword);` | 获取明文密码 | 强制 | 高 | 避免获取明文密码，应使用加密机制 |
| 18 | 第477行 | `userVo.setPassword(getPassword(userVo));` | 在用户对象中设置明文密码 | 强制 | 高 | 避免在用户对象中存储明文密码，应使用加密机制 |
| 19 | 第510行 | `userVo.setPassword(getPassword(userVo));` | 在用户对象中设置明文密码 | 强制 | 高 | 避免在用户对象中存储明文密码，应使用加密机制 |

问题总结：
| 问题级别 | 问题个数 |
|--------|---------|
| 高 | 16 |
| 中 | 3 |