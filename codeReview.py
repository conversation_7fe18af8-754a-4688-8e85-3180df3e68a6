import os
from datetime import datetime
from typing import Dict, Any, Optional

import openai
import requests
from alibabacloud_dingtalk.robot_1_0 import models as dingtalkrobot__1__0_models
from alibabacloud_dingtalk.robot_1_0.client import Client as dingtalkrobot_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from docx import Document
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from langchain.prompts import PromptTemplate
from langchain.text_splitter import RecursiveCharacterTextSplitter
import sys

class DingTalkRobotSender:
    def __init__(self, access_token: str):
        """初始化钉钉机器人发送器"""
        self.access_token = access_token
        self.client = self._create_client()

    @staticmethod
    def _create_client() -> dingtalkrobot_1_0Client:
        """创建钉钉机器人客户端"""
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkrobot_1_0Client(config)

    def send_file(
            self,
            robot_code: str,
            token: str,
            media_id: str,
            file_name: str,
            file_type: str,
            msg_key: str = 'sampleFile'
    ) -> Dict[str, Any]:
        """
        发送文件到钉钉组织群
        :param robot_code: 机器人唯一标识
        :param token: 安全令牌
        :param media_id: 媒体文件ID
        :param file_name: 文件名
        :param file_type: 文件类型(如docx、xlsx等)
        :param msg_key: 消息类型，默认为文件类型
        :return: 发送结果
        """
        try:
            # 构建消息参数
            msg_param = {
                "mediaId": media_id,
                "fileName": file_name,
                "fileType": file_type
            }

            # 设置请求头和请求体
            headers = dingtalkrobot__1__0_models.OrgGroupSendHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token

            request = dingtalkrobot__1__0_models.OrgGroupSendRequest(
                msg_param=str(msg_param),
                msg_key=msg_key,
                robot_code=robot_code,
                token=token
            )

            # 发送请求并返回结果
            response = self.client.org_group_send_with_options(
                request, headers, util_models.RuntimeOptions()
            )

            return {"success": True, "response": response}

        except Exception as err:
            return {"success": False, "error": f"{err.code if hasattr(err, 'code') else 'Unknown'}: {str(err)}"}


def load_prompt_template(template_path):
    """从文件加载提示词模板，支持相对路径"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        full_path = os.path.join(script_dir, template_path)

        with open(full_path, 'r', encoding='utf-8') as file:
            return file.read()
    except Exception as e:
        print(f"加载提示词模板失败: {str(e)}")
        return """作为一名资深Java代码评审员，请对以下Java代码进行全面评审。
        请提供详细的反馈，包括但不限于：
        1. 代码结构和组织
        2. 遵循Java最佳实践的情况
        3. 潜在的bug或问题
        4. 性能考虑
        5. 安全问题
        6. 可读性和可维护性
        7. 建议的改进

        代码文件: {java_file}

        代码内容: {context}

        评审意见:"""


def create_docx_review_report(review_data, output_file="代码评审报告.docx"):
    """创建DOCX格式的评审报告"""
    doc = Document()

    # 添加标题页
    title = doc.add_heading('Java代码评审报告', 0)
    title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

    # 添加生成日期
    doc.add_paragraph(
        f'生成日期: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}').alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    doc.add_page_break()

    # 添加评审内容
    for file_review in review_data:
        doc.add_heading(f'文件: {file_review["file_path"]}', level=2)

        doc.add_heading('评审意见', level=3)
        doc.add_paragraph(file_review["review"])

        doc.add_page_break()

    # 保存文档
    doc.save(output_file)
    return output_file


def review_java_code(directory_path, output_file="代码评审报告.docx",
                     base_url="https://api.openai.com/v1",
                     api_key=None,
                     prompt_template_path="project_rule.md",
                     dingtalk_webhook=None,
                     dingtalk_secret=None,
                     model="Qwen_Qwen3-32B-AWQ",
                     dingtalk_token = None,
                     robot_code = None,
                     app_key = None,
                     app_secret = None):
    """评审Java代码并智能发送DOCX消息"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    directory_path = os.path.join(script_dir, directory_path) if not os.path.isabs(directory_path) else directory_path

    client = openai.OpenAI(base_url=base_url, api_key=api_key)
    java_files = [os.path.join(root, file) for root, _, files in os.walk(directory_path) for file in files if
                  file.endswith('.java')]

    if not java_files:
        print("未找到Java文件")
        return

    template_content = load_prompt_template(prompt_template_path)
    PROMPT = PromptTemplate(template=template_content, input_variables=["context", "java_file"])

    review_data = []
    for java_file in java_files:
        try:
            with open(java_file, 'r', encoding='utf-8') as f:
                code = f.read()

            code_chunk = RecursiveCharacterTextSplitter(chunk_size=10000).split_text(code)[0]
            prompt = PROMPT.format(context=code_chunk, java_file=os.path.relpath(java_file, directory_path))
            review = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            ).choices[0].message.content

            review_data.append({
                "file_path": os.path.relpath(java_file, directory_path),
                "review": review
            })

        except Exception as e:
            review_data.append({
                "file_path": os.path.relpath(java_file, directory_path),
                "code_snippet": "读取文件失败",
                "review": f"错误信息: {str(e)}"
            })

    # 生成DOCX报告
    docx_file_path = create_docx_review_report(review_data, output_file)

    # 获取access_token
    access_token = get_dingtalk_access_token(app_key, app_secret)
    if access_token:
        print(f"成功获取access_token：{access_token}")
    else:
        print("获取access_token失败，请检查参数或网络")

    if dingtalk_webhook and dingtalk_secret:
        # 初始化发送器
        sender = DingTalkRobotSender(access_token=access_token)

        # 上传文件到钉钉
        uploadResult = upload_file_to_dingtalk(access_token, docx_file_path)
        print("上传结果文件id:", uploadResult.get('media_id'))
        if uploadResult and uploadResult.get('media_id'):
            # 发送文件
            result = sender.send_file(
                robot_code=robot_code,
                token=dingtalk_token,
                media_id=uploadResult.get('media_id'),
                file_name=os.path.basename(docx_file_path),
                file_type='docx'
            )

            print("发送结果:", result)
        else:
            print("文件上传失败，无法发送钉钉消息")


def upload_file_to_dingtalk(access_token, file_path, file_type='file'):
    """
    上传文件到钉钉服务器

    参数:
    access_token (str): 访问令牌
    file_path (str): 本地文件路径
    file_type (str): 文件类型，默认为 'file'

    返回:
    dict: 包含上传结果的字典，如果出错则返回 None
    """
    url = f'https://oapi.dingtalk.com/media/upload?access_token={access_token}'

    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return None

        # 读取文件并准备上传
        with open(file_path, 'rb') as file:
            files = {
                'media': file
            }
            data = {
                'type': file_type
            }

            # 发送 POST 请求
            response = requests.post(url, files=files, data=data)

            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("文件上传成功")
                    return result
                else:
                    print(f"上传失败，错误码: {result.get('errcode')}，错误信息: {result.get('errmsg')}")
            else:
                print(f"请求失败，状态码: {response.status_code}")

    except Exception as e:
        print(f"发生异常: {e}")

    return None


def get_dingtalk_access_token(appkey: str, appsecret: str) -> Optional[str]:
    """
    获取钉钉 access_token（有效期7200秒）

    :param appkey: 钉钉应用的AppKey
    :param appsecret: 钉钉应用的AppSecret
    :return: 成功返回access_token，失败返回None
    """
    url = "https://oapi.dingtalk.com/gettoken"
    params = {
        "appkey": appkey,
        "appsecret": appsecret
    }

    try:
        response = requests.get(url, params=params)
        if response.status_code != 200:
            print(f"请求失败，状态码：{response.status_code}")
            return None

        result: Dict[str, Any] = response.json()
        if result.get("errcode") == 0:
            return result["access_token"]
        else:
            print(f"获取access_token失败，错误码：{result.get('errcode')}，错误信息：{result.get('errmsg')}")
            return None

    except Exception as e:
        print(f"发生异常：{str(e)}")
        return None

if __name__ == "__main__":
    # 配置参数
    config = {
        "directory_to_review": r"D:\pythonWorkplace\AICodeReview\maintenance",
        # "directory_to_review": r"D:\WorkPlace\energy_4.3\energy-base\cet-eem-maintenance",
        # "custom_base_url": "https://api.aigc369.com/v1",
        "custom_base_url": "http://172.17.6.105:40000/v1",
        "custom_api_key": "sk-D6l2ztCwPrtMBGDhxVTtUczchoTezsqor0Oy7l2v4HhkVz47",
        "prompt_template": "project_rule.md",
        "dingtalk_webhook": "https://oapi.dingtalk.com/robot/send?access_token=527d8e225a110b74c0da7d4a022d01eb38ea3152ef12ba7f7dc0d21f959adaca",
        "dingtalk_secret": "SEC0b2280807777625307a30d716b5f29daac75b14472057170a1f9702f5bc8fd34",
        # "model": "gpt-3.5-turbo",
        # "model": "qwq-32b",
        # "model": "[105]Qwen_Qwen3-32B-AWQ",
        "model": "qwen-70b",
        "dingtalk_token": "527d8e225a110b74c0da7d4a022d01eb38ea3152ef12ba7f7dc0d21f959adaca",
        "robot_code":"dingjttniqev4rgat7tq",
        "app_key":"dingjttniqev4rgat7tq",
        "app_secret":"SqAVC_YnNeNdUgzuunP974hlGqoHUgaFv0DcwzoJRHVnh61QG_HRkp2fBCnquOLq"
    }

    review_java_code(
        directory_path=config["directory_to_review"],
        base_url=config["custom_base_url"],
        api_key=config["custom_api_key"],
        prompt_template_path=config["prompt_template"],
        dingtalk_webhook=config["dingtalk_webhook"],
        dingtalk_secret=config["dingtalk_secret"],
        model=config["model"],
        dingtalk_token=config["dingtalk_token"],
        robot_code=config["robot_code"],
        app_key=config["app_key"],
        app_secret=config["app_secret"]
    )

