<template>
  <div class="page">
    <div v-for="(item,index) in model" :key="index" class="item">
        <div>{{ item.text }}：</div>
        <div>{{ statistics[item.label] }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { apiService } from '../api'

const model = [
  { label: 'programmingLanguage', text: '语言' },
  { label: 'totalSuggestionCount', text: '总建议数量' },
  { label: 'totalAdoptedCount', text: '总采纳建议数量' },
  { label: 'adoptionRate', text: '采纳率' },
];
const statistics = ref({}); 


const getStatistics =() => {
  try {
    apiService.queryStatistics().then(res => {
        statistics.value = res.data[0];
    });
  } catch (error) {
    ElMessage.error('获取失败，请稍后重试');
  }
};

getStatistics();
</script>
<style scoped>
.page {
  width: 100%;
  height: 100%;
  padding:24px;
  box-sizing: border-box;
  .item {
    display:flex;
    flex-direction: row;
  }
}
</style>