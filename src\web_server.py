import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import List
import zipfile
from fastapi import FastAPI, UploadFile, File, HTTPException, BackgroundTasks, Form, Body
from fastapi.responses import JSONResponse, FileResponse,StreamingResponse
import asyncio
from src.services.code_review import generate_code_review, summarize_reviews
from src.utils.CodeReviewStatusManager import CodeReviewStatusManager
from src.utils.file_util import save_file, save_content
from typing import List, Dict, Optional
import shutil
import tempfile
from src.utils.db_util import insert_user_coding_session, get_all_coding_sessions
from fastapi import Query
import json

app = FastAPI(title="Code Review API")
# 初始化状态管理器
status_manager = CodeReviewStatusManager()

async def run_code_review_async(files: list, result_dir: str):
    """后台异步执行代码评审任务（单线程顺序处理）"""

    def process_file(file_info):
        """同步处理单个文件的完整评审流程"""
        file_path = os.path.join(result_dir, file_info["filename"])
        try:
            # 更新当前处理文件
            asyncio.run(status_manager.update_status(result_dir, "processing", current_file=file_info['filename']))
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                code_content = f.read()

            # 生成评审结果
            review_result = generate_code_review(code_content, '')

            # 保存评审结果
            save_file(result_dir, file_info, review_result)

            # 生成总结
            reviews = summarize_reviews(review_result)

            # 保存总结
            save_content(result_dir, file_info, reviews)

            # 更新进度
            asyncio.run(status_manager.increment_progress(result_dir))

        except Exception as e:
            error_msg = f"文件 {file_info['filename']} 评审失败: {str(e)}"
            print(error_msg)
            asyncio.run(status_manager.update_status(result_dir, "failed", error=error_msg))

    # 顺序处理每个文件，避免多线程并发问题
    for file_info in files:
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, process_file, file_info)

    # 如果没有失败，标记为完成
    current_status = await status_manager.get_status(result_dir)
    if current_status and current_status.get("status") != "failed":
        await status_manager.update_status(result_dir, "completed")

@app.post("/api/code_review")
async def code_review(
        background_tasks: BackgroundTasks,
        files: List[UploadFile] = File(..., description="至少上传一个文件")
):
    """
    代码评审接口
    - 接收多个Java文件（至少一个）
    - 立即返回结果目录路径
    - 后台异步执行代码评审
    """
    # 验证至少一个文件
    if not files or len(files) == 0:
        raise HTTPException(status_code=400, detail="至少需要上传一个Java文件")

    # 创建结果目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_dir = os.path.join("results", f"review_{timestamp}_{uuid.uuid4().hex[:8]}")
    os.makedirs(result_dir, exist_ok=True)

    # 保存文件信息用于后台任务
    file_info_list = []

    # 保存上传的文件到结果目录
    for file in files:
        if not file.filename.endswith(".java"):
            # 注意：这里我们只记录而不立即终止，因为已经上传了其他文件
            print(f"警告: 跳过非Java文件 {file.filename}")
            continue

        file_path = os.path.join(result_dir, file.filename)
        content = await file.read()

        # 保存原始文件
        with open(file_path, 'wb') as f:
            f.write(content)

        file_info_list.append({
            "filename": file.filename,
            "path": file_path
        })

    # 如果没有有效的Java文件
    if not file_info_list:
        raise HTTPException(status_code=400, detail="未找到有效的Java文件")

    # 添加后台任务
    background_tasks.add_task(run_code_review_async, file_info_list, result_dir)

    # 立即返回结果目录路径
    return JSONResponse(content={
        "code": 0,
        "message": "代码评审任务已开始，请稍后查看结果",
        "data": {
            "result_dir": result_dir,
            "files_received": [info["filename"] for info in file_info_list]
        }
    })


@app.post("/api/download")  # 修改为POST请求
async def download_zip(path: str = Form(...)):
    """
    打包指定目录为zip文件并下载
    - path: 要打包的目录相对路径
    """
    target_dir = get_absolute_path(path)

    # 验证目录
    if not target_dir.exists():
        raise HTTPException(status_code=404, detail="目录不存在")
    if not target_dir.is_dir():
        raise HTTPException(status_code=400, detail="路径不是目录")

    # 创建临时zip文件
    zip_filename = f"{target_dir.name}.zip"
    temp_dir = tempfile.mkdtemp()
    temp_zip_path = Path(temp_dir) / zip_filename

    # 打包目录为zip
    try:
        with zipfile.ZipFile(temp_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(target_dir):
                for file in files:
                    file_path = Path(root) / file
                    arcname = file_path.relative_to(target_dir.parent)
                    zipf.write(file_path, arcname)

        # 返回文件下载响应（使用流式响应自动清理临时文件）
        def iter_file():
            with open(temp_zip_path, "rb") as f:
                yield from f
            shutil.rmtree(temp_dir)  # 清理临时文件

        return StreamingResponse(
            iter_file(),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename={zip_filename}"}
        )

    except Exception as e:
        shutil.rmtree(temp_dir, ignore_errors=True)
        raise HTTPException(status_code=500, detail=f"创建压缩包失败: {str(e)}")


# 添加状态查询API
@app.get("/api/code_review/status/{result_dir}")
async def get_review_status(result_dir: str):
    """获取代码评审任务状态"""
    status = await status_manager.get_status(result_dir)
    if not status:
        raise HTTPException(status_code=404, detail="任务不存在或已过期")

    return JSONResponse(content={
        "code": 0,
        "data": {
            "status": status["status"],
            "progress": status["progress"],
            "current_file": status["current_file"],
            "processed_files": status["processed_files"],
            "total_files": status["total_files"],
            "start_time": status["start_time"],
            "end_time": status["end_time"],
            "error": status["error"]
        }
    })


@app.get("/api/list-files/", response_model=Dict[str, List[str]])
async def list_files(directory: str) -> Dict[str, List[str]]:
    """
    获取目录下的文件列表
    - directory: 前端传递的目录相对路径
    """
    target_dir = get_absolute_path(directory)

    # 验证路径
    if not target_dir.exists():
        raise HTTPException(status_code=404, detail="目录不存在")
    if not target_dir.is_dir():
        raise HTTPException(status_code=400, detail="路径不是目录")

    # 获取所有文件和目录
    items = []
    for item in target_dir.iterdir():
        if item.is_file():
            items.append(f"📄 {item.name}")
        elif item.is_dir():
            items.append(f"📁 {item.name}")

    return {"files": items}


# @app.get("/get-file/", response_model=Dict[str, str])
# async def get_file(file_path: str) -> Dict[str, str]:
#     """
#     读取并返回文件内容
#     - file_path: 前端传递的文件相对路径
#     """
#     target_file = get_absolute_path(file_path)
#
#     # 验证路径
#     if not target_file.exists():
#         raise HTTPException(status_code=404, detail="文件不存在")
#     if not target_file.is_file():
#         raise HTTPException(status_code=400, detail="路径不是文件")
#
#     # 读取文件内容
#     try:
#         with open(target_file, "r", encoding="utf-8") as f:
#             content = f.read()
#         return {"content": content}
#     except UnicodeDecodeError:
#         # 尝试读取二进制文件
#         try:
#             with open(target_file, "rb") as f:
#                 content = f.read().hex()
#             return {"content": f"<二进制文件 十六进制预览>\n{content[:1000]}..."}
#         except Exception:
#             raise HTTPException(status_code=400, detail="无法读取文件内容")
@app.get("/api/review/")
async def get_directory_files_structured(dir_path: str = Query(...)):
    """
    读取目录下的review.json文件内容
    - dir_path: 前端传递的目录相对路径
    - 返回: review.json文件内容（预期为JSON格式）
    """
    target_dir = get_absolute_path(dir_path)
    if not target_dir.exists():
        raise HTTPException(status_code=404, detail="目录不存在")
    if not target_dir.is_dir():
        raise HTTPException(status_code=400, detail="路径不是目录")

    # 定位review.json文件
    review_file = target_dir / "review.json"

    # 检查文件是否存在
    if not review_file.exists():
        raise HTTPException(status_code=404, detail="review.json文件不存在")
    if not review_file.is_file():
        raise HTTPException(status_code=400, detail="review.json不是文件")

    try:
        # 读取并返回文件内容
        with open(review_file, "r", encoding="utf-8") as f:
            return json.load(f)
    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail="review.json文件格式错误，无法解析")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取文件时出错: {str(e)}")


# 获取当前文件所在目录作为基础目录
BASE_DIR = Path(__file__).parent.resolve()
print(f"服务基础目录: {BASE_DIR}")
def is_safe_path(base: Path, path: str) -> bool:
    """检查路径是否在允许的基目录内"""
    try:
        # 解析为绝对路径
        target = (base / path).resolve()
        base_resolved = base.resolve()

        # 检查目标路径是否在基目录下
        return base_resolved in target.parents or target == base_resolved
    except Exception:
        return False


def get_absolute_path(relative_path: str) -> Path:
    """获取相对于BASE_DIR的绝对路径（带安全检查）"""
    if not is_safe_path(BASE_DIR, relative_path):
        raise HTTPException(status_code=403, detail="访问路径受限")
    return BASE_DIR / relative_path


@app.post("/api/save/statistics")
async def adopt_review_suggestions(
    programming_language: str = Body(...),
    suggestion_count: int = Body(...),
    adopted_count: int = Body(...)
    # session_duration: int = Body(...)
):
    try:
        insert_user_coding_session(
            programming_language=programming_language,
            suggestion_count=suggestion_count,
            adopted_count=adopted_count,
            # session_duration=session_duration
        )
        return {"code": 0, "message": "保存成功"}
    except Exception as e:
        return {"code": 1, "message": f"保存失败: {str(e)}"}


@app.get("/api/query/statistics")
async def get_coding_statistics():
    """
    获取按编程语言分类的统计信息
    返回格式：
    {
        "code": 0,
        "message": "成功",
        "data": [
            {
                "programmingLanguage": "Java",
                "totalSuggestionCount": 200,
                "totalAdoptedCount": 30,
                "adoptionRate": "15%"
            },
            ...
        ]
    }
    """
    try:
        # 获取所有原始数据
        records = get_all_coding_sessions()

        # 按语言分组统计
        stats_by_lang = {}
        for record in records:
            lang = record["programming_language"]

            # 初始化语言统计
            if lang not in stats_by_lang:
                stats_by_lang[lang] = {
                    "programmingLanguage": lang,
                    "totalSuggestionCount": 0,
                    "totalAdoptedCount": 0
                }

            # 累加统计值
            stats_by_lang[lang]["totalSuggestionCount"] += record["suggestion_count"]
            stats_by_lang[lang]["totalAdoptedCount"] += record["adopted_count"]

        # 计算采纳率并格式化结果
        result = []
        for lang, stats in stats_by_lang.items():
            suggestion_count = stats["totalSuggestionCount"]
            adopted_count = stats["totalAdoptedCount"]

            # 计算采纳率（避免除零错误）
            if suggestion_count > 0:
                adoption_rate = round(adopted_count * 100 / suggestion_count)
            else:
                adoption_rate = 0

            result.append({
                "programmingLanguage": lang,
                "totalSuggestionCount": suggestion_count,
                "totalAdoptedCount": adopted_count,
                "adoptionRate": f"{adoption_rate}%"
            })

        # 按语言名称排序
        result.sort(key=lambda x: x["programmingLanguage"])

        return {
            "code": 0,
            "message": "成功",
            "data": result
        }

    except Exception as e:
        # 记录日志（实际项目中应添加日志记录）
        print(f"统计查询失败: {str(e)}")

        return {
            "code": 1,
            "message": f"统计查询失败: {str(e)}",
            "data": []
        }

