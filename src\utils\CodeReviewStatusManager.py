from typing import Dict, Any, List, Optional
from collections import defaultdict
import asyncio
import time


# 内存状态存储
class CodeReviewStatusManager:
    _instance = None
    _lock = asyncio.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._status_store = defaultdict(dict)
        return cls._instance

    async def update_status(
            self,
            result_dir: str,
            status: str,
            current_file: Optional[str] = None,
            progress: Optional[float] = None,
            error: Optional[str] = None
    ):
        """更新任务状态"""
        async with self._lock:
            if result_dir not in self._status_store:
                self._status_store[result_dir] = {
                    "start_time": time.time(),
                    "status": status,
                    "progress": 0.0,
                    "processed_files": 0,
                    "total_files": 0,
                    "current_file": None,
                    "end_time": None,
                    "error": None
                }

            entry = self._status_store[result_dir]
            entry["status"] = status

            if current_file:
                entry["current_file"] = current_file

            if progress is not None:
                entry["progress"] = progress

            if error:
                entry["error"] = error

            if status == "completed" or status == "failed":
                entry["end_time"] = time.time()

    async def start_task(self, result_dir: str, total_files: int):
        """初始化新任务"""
        async with self._lock:
            self._status_store[result_dir] = {
                "start_time": time.time(),
                "status": "processing",
                "progress": 0.0,
                "processed_files": 0,
                "total_files": total_files,
                "current_file": None,
                "end_time": None,
                "error": None
            }

    async def increment_progress(self, result_dir: str):
        """更新处理进度"""
        async with self._lock:
            if result_dir in self._status_store:
                entry = self._status_store[result_dir]
                entry["processed_files"] += 1
                if entry["total_files"] > 0:
                    entry["progress"] = min(
                        100.0,
                        round(entry["processed_files"] / entry["total_files"] * 100, 1)
                    )

    async def get_status(self, result_dir: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        async with self._lock:
            return self._status_store.get(result_dir, None)

    async def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        async with self._lock:
            current_time = time.time()
            to_delete = []

            for result_dir, status in list(self._status_store.items()):
                if "end_time" in status and status["end_time"]:
                    age_hours = (current_time - status["end_time"]) / 3600
                    if age_hours > max_age_hours:
                        to_delete.append(result_dir)

            for result_dir in to_delete:
                del self._status_store[result_dir]