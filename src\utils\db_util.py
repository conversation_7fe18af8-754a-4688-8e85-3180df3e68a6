import psycopg2
from psycopg2.extras import RealDictCursor
from contextlib import contextmanager

DB_CONFIG = {
    'host': '************',
    'port': 9845,
    'user': 'postgres',
    'password': 'Ceiec4567%%',
    'dbname': 'CodeView'
}

@contextmanager
def get_conn():
    conn = psycopg2.connect(**DB_CONFIG)
    try:
        yield conn
    finally:
        conn.close()

def insert_user_coding_session(programming_language: str, suggestion_count: int, adopted_count: int):
    sql = '''
    INSERT INTO code_review_record (programming_language, suggestion_count, adopted_count)
    VALUES (%s, %s, %s)
    '''
    with get_conn() as conn:
        with conn.cursor() as cur:
            cur.execute(sql, (programming_language, suggestion_count, adopted_count))
            conn.commit()


def get_coding_sessions(page: int = 1, page_size: int = 10):
    """
    分页查询编程会话记录
    - page: 页码（从1开始）
    - page_size: 每页数量
    """
    offset = (page - 1) * page_size
    sql = '''
    SELECT 
        id,
        programming_language,
        suggestion_count,
        adopted_count,
        created_at
    FROM code_review_record
    ORDER BY created_at DESC
    LIMIT %s OFFSET %s
    '''
    with get_conn() as conn:
        with conn.cursor() as cur:
            cur.execute(sql, (page_size, offset))
            results = cur.fetchall()

    columns = [desc[0] for desc in cur.description]
    return [dict(zip(columns, row)) for row in results]


def get_all_coding_sessions():
    """
    查询所有编程会话记录
    返回: 包含所有记录的列表
    """
    sql = '''
    SELECT 
        id,
        programming_language,
        suggestion_count,
        adopted_count
    FROM code_review_record
    '''
    with get_conn() as conn:
        with conn.cursor() as cur:
            cur.execute(sql)
            results = cur.fetchall()

    # 将结果转换为字典列表（更易用的格式）
    columns = [desc[0] for desc in cur.description]
    return [dict(zip(columns, row)) for row in results]