# import os
#
# from langchain.prompts import Chat<PERSON>romptTemplate
# from langchain_core.output_parsers import StrOutputParser
# from langchain_core.runnables import RunnablePassthrough
#
# from src.utils.model_operation import create_llm_model
# from src.prompt.prompt import load_prompt_template
#
# # model_name = 'deepseek-ai/DeepSeek-V3'
# # model_name = 'deepseek-ai/DeepSeek-R1-Distill-Qwen-32B'
# # model_name = 'deepseek-ai/DeepSeek-R1'
# model_name = 'Qwen/Qwen3-235B-A22B'
# model_url = 'https://api.siliconflow.cn/v1/'
# model_api_key = os.getenv('GJ_KEY')
# max_tokens = 4096  # 新增最大token数配置
#
# llm = create_llm_model(model_name, model_url, model_api_key, max_tokens=max_tokens)  # 添加max_tokens参数
#
# prompt = load_prompt_template()
# # print("提示词：", prompt)
#
# current_path = os.getcwd()
# parent_path = os.path.dirname(current_path)
# code_path = os.path.join(parent_path, 'review_code')
# output_path = os.path.join(parent_path, 'output')
#
# code = ""
# # 依次读取代码目录下的文件内容
# for file in os.listdir(code_path):
#     file_path = os.path.join(code_path, file)
#     if os.path.isfile(file_path):
#         with open(file_path, 'r', encoding='utf-8') as f:
#             tmp_code = f.read()
#             code = code + tmp_code
#
# if len(code) == 0:
#     print("没有找到代码文件")
#     exit()
#
# prompt = ChatPromptTemplate.from_messages(
#     [
#         ("human", prompt)
#     ]
# )
#
# # print(prompt.format_prompt(code=code).to_string())
#
# chain = prompt | llm | StrOutputParser()
#
#
# def generate_code_review(code: str, question: str):
#     """
#     生成代码评审报告的生成器函数
#     """
#     # 动态组合系统提示词
#     system_prompt = f"""
#     你是一个资深的编码专家，请你按照以下要求评审代码：
#     {question}
#
#     ## 代码评审依据规则
#     1. 使用阿里巴巴的Java代码规范
#     2. 检查潜在问题包括：代码漏洞、规范违反、性能瓶颈、代码风格问题
#     """
#
#     # 构建prompt模板
#     prompt = ChatPromptTemplate.from_messages([
#         ("system", system_prompt),
#         ("human", "## 代码\n{code}")
#     ])
#
#     # 构建chain
#     chain = prompt | llm | StrOutputParser()
#
#     # 流式处理
#     try:
#         for chunk in chain.stream({"code": code}):
#             yield chunk
#     except Exception as e:
#         yield f"评审过程发生错误：{str(e)}"
#
# # 流式输出处理
# output_file = os.path.join(output_path, "report.md")
# with open(output_file, "w", encoding="utf-8") as f:  # 清空已有内容
#     pass
#
# for chunk in chain.stream({"code": code}):
#     print(chunk, end="", flush=True)  # 实时打印输出
#     with open(output_file, "a", encoding="utf-8") as f:  # 追加模式写入
#         f.write(chunk)
#
#
