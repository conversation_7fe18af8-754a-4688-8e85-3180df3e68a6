import os


def save_content(result_dir: str, file, content):
    result_file = os.path.join(result_dir, "review.json")
    with open(result_file, "w", encoding="utf-8") as f:
        f.write(content)

def save_single_file(result_dir: str, file, res):
    result_file = os.path.join(result_dir, f"{file['filename']}_{res['rule_name']}_review.md")
    with open(result_file, "w", encoding="utf-8") as f:
        f.write(res['review_result'])

def save_file(result_dir: str, file, res_list):
    # 保存结果到文件
    if isinstance(res_list, list):
        for res in res_list:
            save_single_file(result_dir, file, res)
    else:
        save_single_file(result_dir, file, res_list)