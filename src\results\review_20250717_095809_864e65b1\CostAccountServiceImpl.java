//package com.cet.eem.fusion.cost.core.service.impl;
//
//
//import com.cet.eem.fusion.common.def.common.ColumnDef;
//import com.cet.eem.fusion.common.entity.common.TimeValueData;
//import com.cet.eem.fusion.common.modelutils.service.EemModelDataService;
//import com.cet.eem.fusion.common.utils.time.TimeUtil;
//
//import com.cet.eem.fusion.cost.core.service.CostAccountService;
//import com.cet.electric.modelservice.common.entity.IdTextPair;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 成本核算业务
// */
//@Service
//public class CostAccountServiceImpl implements CostAccountService {
//
//    @Autowired
//    SchemeConfigService schemeConfigService;
//    @Autowired
//    private EemModelDataService eemModelDataService;
//
//    @Override
//    public List<CostsAccount> queryCostValueData(List<ObjectCostValue> costValue, CostSearchVo searchVo) {
//        // 封装map格式
//        Map<BasicCostAccount, ObjectCostValue> costAccountMap = getCostAccountMap(costValue);
//        List<CostsAccount> costsAccounts = new ArrayList<>();
//        Result<List<IdTextPair>> enumrationByModel = eemModelDataService.getEnumrationByModel(ColumnDef.ENERGY_TYPE);
//        Map<Integer, String> typrRelation = getTypeRelation(enumrationByModel, searchVo);
//        // 不同节点
//        for (Long id : searchVo.getIds()) {
//            for (Integer type : searchVo.getEnergyTypes()) {
//                handleOfType(costAccountMap, searchVo, costsAccounts, id, type, typrRelation);
//            }
//        }
//        return costsAccounts;
//    }
//
//
//    private Map<BasicCostAccount, ObjectCostValue> getCostAccountMap(List<ObjectCostValue> costValue) {
//        Map<BasicCostAccount, ObjectCostValue> map = new HashMap<>();
//        if (CollectionUtils.isEmpty(costValue)) {
//            return map;
//        }
//        for (ObjectCostValue vo : costValue) {
//            BasicCostAccount basicCostAccount = new BasicCostAccount(vo.getObjectid(), vo.getObjectlabel(), vo.getAggregationcycle(), TimeUtil.timestamp2LocalDateTime(vo.getLogtime()), vo.getEnergytype());
//            map.put(basicCostAccount, vo);
//        }
//        return map;
//    }
//
//    private Map<Integer, String> getTypeRelation(Result<List<IdTextPair>> enumrationByModel, CostSearchVo searchVo) {
//        List<Integer> energyTypes = searchVo.getEnergyTypes();
//        List<IdTextPair> data = enumrationByModel.getData();
//        Map<Integer, String> map = new HashMap<>();
//        for (Integer type : energyTypes) {
//            for (IdTextPair item : data) {
//                if (item.getId().equals(type)) {
//                    map.put(type, item.getText());
//                }
//            }
//        }
//        return map;
//    }
//
//    private void handleOfType(Map<BasicCostAccount, ObjectCostValue> costAccountMap, CostSearchVo searchVo, List<CostsAccount> costsAccount, Long id, Integer type, Map<Integer, String> typrRelation) {
//        CostsAccount account = new CostsAccount();
//        List<LocalDateTime> timeRange = getTimeRange(searchVo);
//        account.setObjectId(id);
//        account.setObjectLabel(searchVo.getModelLabel());
//        account.setType(type);
//        // 获取类型对应名称
//        if (!typrRelation.isEmpty() && typrRelation.containsKey(type)) {
//            account.setTypeName(typrRelation.get(type));
//        }
//        List<TimeValueData> data = new ArrayList<>();
//        for (LocalDateTime time : timeRange) {
//            //
//            Double value;
//            BasicCostAccount vo = new BasicCostAccount(id, searchVo.getModelLabel(), searchVo.getCycle(), time, type);
//            if (costAccountMap.containsKey(vo)) {
//                value = costAccountMap.get(vo).getValue();
//            } else {
//                value = null;
//            }
//            data.add(new TimeValueData(time, value));
//        }
//        account.setValues(data);
//        costsAccount.add(account);
//    }
//
//    private List<LocalDateTime> getTimeRange(CostSearchVo searchVo) {
//        // 时间区间
//        long startTime = searchVo.getStartTime();
//        long endTime = searchVo.getEndTime();
//        int cycle = searchVo.getCycle();
//        List<Long> timeRange = TimeUtil.getTimeRange(startTime, endTime, cycle);
//        List<LocalDateTime> finalTimeRange = new ArrayList<>();
//        for (Long time : timeRange) {
//            LocalDateTime finalTime = TimeUtil.timestamp2LocalDateTime(time);
//            finalTimeRange.add(finalTime);
//        }
//        return finalTimeRange;
//    }
//}
