import { createApp } from 'vue'
import './style.css'
import App from './App.vue'

// 引入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 引入Axios
import axios from 'axios'
import VueAxios from 'vue-axios'

// 创建应用实例
const app = createApp(App)

// 配置Axios
// axios.defaults.baseURL = '/api'
app.config.globalProperties.$axios = axios

// 使用插件
app.use(ElementPlus)
app.use(VueAxios, axios)

// 挂载应用
app.mount('#app')
