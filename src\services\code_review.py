import os

from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

from src.prompt.prompt import load_prompt_template
from src.utils.model_operation import create_llm_qwen3, review_create_llm_qwen3


def read_local_code():
    current_path = os.getcwd()
    parent_path = os.path.dirname(current_path)
    code_path = os.path.join(parent_path, 'review_code')
    output_path = os.path.join(parent_path, 'output')

    code = ""
    # 依次读取代码目录下的文件内容
    for file in os.listdir(code_path):
        file_path = os.path.join(code_path, file)
        if os.path.isfile(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                tmp_code = f.read()
                code = code + tmp_code
    return code


def java_code_review(code: str, question: str) -> str:
    review = generate_code_review(code, question)
    return summarize_reviews(review)


def generate_code_review(code: str, question: str):
    # 创建大模型对象
    # llm = create_llm_qwen3()

    # 修改提示词加载逻辑（新增条件判断）
    if question.strip():
        system_prompts = [{"rule_name": "用户自定义问题", "prompt": question}]  # 使用自定义结构
    else:
        system_prompts = [
            {"rule_name": prompt["rule_name"], "prompt": prompt["prompt_content"]}
            for prompt in load_prompt_template()
        ]

    results = []  # 存储所有提示词的结果

    # 遍历每个提示词
    for prompt_item in system_prompts:
        current_result = _generate_review_result(create_llm_qwen3(), code, prompt_item["prompt"])
        results.append({
            "rule_name": prompt_item["rule_name"],
            "review_result": current_result
        })

    return results  # 返回结构化结果列表


# def summarize_reviews(reviews: list,
#                       summary_prompt: str = "请将如下多个评审意见汇总成一份JSON格式的报告。每个问题应包含以下字段：'problem_id'(问题编号), 'location'(问题位置), 'description'(问题描述), 'severity'(严重程度), 'suggestion'(修改建议)。请确保返回纯JSON格式，不要包含其他文本或注释，位置需要带上文件名，例如：CombineCostServiceImpl.java:41") -> str:
#     """
#     汇总多个评审结果并以JSON格式返回
#
#     参数:
#         reviews: 结构化评审结果列表
#         summary_prompt: 汇总提示词（需明确要求JSON格式）
#
#     返回:
#         str: JSON格式的汇总报告
#     """
#     from langchain_core.output_parsers import JsonOutputParser
#     from langchain_core.pydantic_v1 import BaseModel, Field
#     from langchain_core.prompts import ChatPromptTemplate
#     import json
#
#     # 定义期望的问题结构
#     class Problem(BaseModel):
#         problem_id: int = Field(description="唯一的问题编号")
#         location: str = Field(description="问题在文档中的具体位置")
#         description: str = Field(description="问题的详细描述")
#         severity: str = Field(description="问题严重程度：高、中、低")
#         suggestion: str = Field(description="具体的修改建议")
#
#     # 定义整体报告结构
#     class ReviewSummary(BaseModel):
#         total_problems: int = Field(description="问题总数")
#         problems: list[Problem] = Field(description="问题列表")
#         overall_summary: str = Field(description="总体评价摘要")
#
#     llm = review_create_llm_qwen3()
#     parser = JsonOutputParser(pydantic_object=ReviewSummary)
#
#     # 拼接所有评审结果作为输入
#     combined_reviews = "\n\n".join(
#         [f"【{review['rule_name']}】\n{review['review_result']}"
#          for review in reviews]
#     )
#
#     # 构建包含格式指示的提示词
#     prompt = ChatPromptTemplate.from_messages([
#         ("system", summary_prompt + "\n{format_instructions}"),
#         ("human", "以下是多个评审意见:\n{reviews}")
#     ])
#
#     # 获取JSON格式指示
#     format_instructions = parser.get_format_instructions()
#
#     chain = prompt | llm | parser
#
#     try:
#         result = chain.invoke({
#             "reviews": combined_reviews,
#             "format_instructions": format_instructions
#         })
#
#         # 转换为格式化的JSON字符串
#         return json.dumps(result, ensure_ascii=False, indent=2)
#
#     except Exception as e:
#         error_msg = f"汇总过程发生错误：{str(e)}"
#         return json.dumps({"error": error_msg}, ensure_ascii=False)

def summarize_reviews(reviews: list,
                      summary_prompt: str = "请将如下多个评审意见汇总成一份JSON格式的报告。每个问题应包含以下字段：'problem_id'(问题编号), 'location'(问题位置), 'description'(问题描述), 'severity'(严重程度), 'suggestion'(修改建议)。请确保返回纯JSON格式，不要包含其他文本或注释，位置需要带上文件名，例如：CombineCostServiceImpl.java:41") -> str:
    """
    汇总多个评审结果并以JSON格式返回（带重试机制）

    参数:
        reviews: 结构化评审结果列表
        summary_prompt: 汇总提示词（需明确要求JSON格式）

    返回:
        str: JSON格式的汇总报告
    """
    from langchain_core.output_parsers import JsonOutputParser
    from langchain_core.pydantic_v1 import BaseModel, Field
    from langchain_core.prompts import ChatPromptTemplate
    import json
    import time
    import random
    from json import JSONDecodeError
    from langchain_core.exceptions import OutputParserException

    # 定义期望的问题结构
    class Problem(BaseModel):
        problem_id: int = Field(description="唯一的问题编号")
        location: str = Field(description="问题在文档中的具体位置")
        description: str = Field(description="问题的详细描述")
        severity: str = Field(description="问题严重程度：高、中、低")
        suggestion: str = Field(description="具体的修改建议")

    # 定义整体报告结构
    class ReviewSummary(BaseModel):
        total_problems: int = Field(description="问题总数")
        problems: list[Problem] = Field(description="问题列表")
        overall_summary: str = Field(description="总体评价摘要")

    llm = review_create_llm_qwen3()
    parser = JsonOutputParser(pydantic_object=ReviewSummary)

    # 拼接所有评审结果作为输入
    combined_reviews = "\n\n".join(
        [f"【{review['rule_name']}】\n{review['review_result']}"
         for review in reviews]
    )

    # 构建包含格式指示的提示词
    prompt = ChatPromptTemplate.from_messages([
        ("system", summary_prompt + "\n{format_instructions}"),
        ("human", "以下是多个评审意见:\n{reviews}")
    ])

    # 获取JSON格式指示
    format_instructions = parser.get_format_instructions()

    chain = prompt | llm | parser

    max_retries = 3  # 最大重试次数
    base_delay = 1  # 基础等待时间(秒)
    last_error = None

    for attempt in range(max_retries):
        try:
            result = chain.invoke({
                "reviews": combined_reviews,
                "format_instructions": format_instructions
            })

            # 转换为格式化的JSON字符串
            return json.dumps(result, ensure_ascii=False, indent=2)

        except (OutputParserException, JSONDecodeError, ValueError) as e:
            last_error = e
            # 指数退避 + 随机抖动
            delay = base_delay * (2 ** attempt) + random.uniform(0, 0.5)
            time.sleep(delay)
        except Exception as e:
            last_error = e
            break  # 非JSON解析错误直接终止重试

    # 重试失败后返回错误信息
    error_msg = f"汇总失败（重试{max_retries}次）: {str(last_error)}"
    return json.dumps({"error": error_msg}, ensure_ascii=False)


def _generate_review_result(llm, code: str, system_prompt: str) -> str:
    """
    封装生成单个评审结果的逻辑
    
    参数:
        llm: 大模型对象
        code: 待评审代码
        system_prompt: 系统提示词
        
    返回:
        str: 评审结果
    """
    # 组装prompt模板
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        ("human", "代码\n{code}")
    ])

    chain = prompt | llm | StrOutputParser()

    try:
        current_result = ""
        # current_result = chain.invoke({"code": code})

        for chunk in chain.stream({"code": code}):
            print(chunk, end="", flush=True)  # 实时打印输出
            current_result += chunk

        # 新增处理逻辑：只保留</think>之后的内容
        # think_tag = "</think>"
        # if think_tag in current_result:
        #     current_result = current_result.split(think_tag)[-1].strip()
        return current_result

    except Exception as e:
        error_msg = f"评审过程发生错误：{str(e)}"
        print(error_msg)
        return error_msg
