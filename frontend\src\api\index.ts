import axios from "axios";

// API响应接口定义，使用泛型T来表示data字段的类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 代码评审响应的data字段接口
export interface CodeReviewData {
  result_dir: string;
  files_received: string[];
}

// 创建axios实例
const api = axios.create({
  // 不设置全局baseURL，因为我们有多个不同的API端点
  timeout: 10000, // 请求超时时间
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    // 例如：添加token
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    return response.data;
  },
  (error) => {
    // 对响应错误做点什么
    if (error.response) {
      // 请求已发出，但服务器响应的状态码不在 2xx 范围内
      console.error("响应错误:", error.response.status, error.response.data);
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      console.error("请求错误:", error.request);
    } else {
      // 发送请求时出了点问题
      console.error("错误:", error.message);
    }
    return Promise.reject(error);
  }
);

// 评审结果接口
export interface ReviewResult {
  fileName: string;
  comments: {
    line: number;
    content: string;
  }[];
}

// 评审进度状态接口
export interface ReviewStatus {
  status: string; // 例如：'pending', 'processing', 'completed', 'failed'
  progress: number; // 进度百分比，0-100
}

// API接口
export const apiService = {
  // 代码评审
  codeReview(files: File[]): Promise<ApiResponse<CodeReviewData>> {
    const formData = new FormData();
    // 正确的做法：循环添加每个文件
    files.forEach((file, index) => {
      formData.append("files", file);
    });
    return api.post("/code-review-api/code_review", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  // 获取代码评审结果
  downLoadReviewResult(
    data: Object
  ): Promise<ApiResponse<ReviewResult[]>> {
    return api.post(
      `/code-review-api/download`,data, {
        headers:{
          "accept": "application/zip",
          // "Content-Type": "multipart/form-data"
        }
      }
    );
  },

  // 获取代码评审进度
  getReviewStatus(resultDir: string): Promise<ApiResponse<ReviewStatus>> {
    // 处理路径中的反斜杠，确保URL格式正确
    console.log(`请求路径：${resultDir}`);

    return api.get(
      `/code-review-api/code_review/status/${encodeURIComponent(resultDir)}`
    );
  },

  // 预览评审文档
  getReviewWord(resultDir: string): Promise<ApiResponse<ReviewStatus>> {
    return api.get(`/code-review-api/get-directory-files/?dir_path=${resultDir}`)
  },

  // 获取代码评审总结
  getReview(resultDir:string): Promise<ApiResponse<ReviewStatus>> {
    return api.get(`/code-review-api/review/?dir_path=${resultDir}`)
  },

  // 评审反馈上传
  saveStatistics(data:Object): Promise<ApiResponse<ReviewStatus>> {
    return api.post(`/code-review-api/save/statistics`, data)
  },

  // 评审汇总
  queryStatistics(data:Object): Promise<ApiResponse<ReviewStatus>> {
    return api.get(`/code-review-api/query/statistics`)
  }
};

export default api;
