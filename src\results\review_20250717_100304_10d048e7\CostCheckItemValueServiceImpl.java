package com.cet.eem.fusion.cost.core.service.impl;


import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.cost.core.dao.CostCheckItemValueDao;
import com.cet.eem.fusion.cost.core.model.CostCheckItemValue;
import com.cet.eem.fusion.cost.core.service.CostCheckItemService;
import com.cet.eem.fusion.cost.core.service.CostCheckItemValueService;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/1/7
 */
@Service
public class CostCheckItemValueServiceImpl implements CostCheckItemValueService {
    @Autowired
    CostCheckItemService costCheckItemService;

    @Autowired
    CostCheckItemValueDao costCheckItemValueDao;

    @Override
    public Map<BaseVo, List<CostCheckItemValue>> queryCost(Integer cycle, LocalDateTime st, LocalDateTime et,
                                                           List<BaseVo> nodes, Integer energyType, Collection<Integer> feeTypes) {
        Map<BaseVo, List<Long>> nodeAndCostItemIdMap = costCheckItemService.queryCostCheckItem(nodes, feeTypes, energyType);
        if (MapUtils.isEmpty(nodeAndCostItemIdMap)) {
            return Collections.emptyMap();
        }

        // 查询当前周期成本数据
        return costCheckItemValueDao.getNoTsCostCheckItemValues(st, et, nodeAndCostItemIdMap, cycle);
    }
}
