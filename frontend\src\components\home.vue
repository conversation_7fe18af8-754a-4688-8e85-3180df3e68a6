<template>
    <div class="page">
        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane label="评审" name="first" />
          <el-tab-pane label="总结" name="second" />
        </el-tabs>
        <div class="content">
            <CodeView v-if="activeName==='first'" />
            <CodeStatistics v-else />
        </div>
    </div>
</template>
<script lang="ts" setup>
import CodeView from './CodeView.vue'
import CodeStatistics from "./CodeStatistics.vue";
import { ref } from 'vue'

const activeName = ref('first');
</script>
<style scoped>
.page {
  width:100%;
  height:100%;
  display:flex;
  flex-direction: column;
  overflow-y: hidden;
  .content {
    flex: auto;
  }
  :deep(.el-tabs__item:nth-child(2)) {
    padding-left:20px !important;
  }
}
</style>