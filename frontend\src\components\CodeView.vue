<template>
  <div class="code-review">
    <div class="left">
      <el-upload
        v-model:file-list="fileList"
        class="upload-demo"
        action="#"
        accept=".java"
        multiple
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        :limit="5"
        :auto-upload="false"
        :on-exceed="handleExceed"
      >
        <el-button type="primary">上传文件</el-button>
        <template #tip>
          <div class="el-upload__tip">允许上传至多5个java文件</div>
        </template>
      </el-upload>
      <el-button
        @click="doCodeReview"
        :loading="loading"
        :disabled="fileList.length === 0"
        type="primary"
        >开始评审</el-button
      >
      <el-button
        @click="getReviewResult"
        :loading="loadingResult"
        :disabled="!reviewIsFinish || !resultDir"
        type="success"
        style="margin-top: 10px"
        >查看评审结果</el-button
      >
    </div>
    <div class="right">
      <!-- 评审进度显示 -->
      <div v-if="reviewStatus && !reviewList" class="review-status">
        <h3>代码评审进度</h3>
        <el-card class="status-card">
          <div class="status-info">
            <div class="status-label">
              状态:
              <span :class="`status-${reviewStatus.status}`">{{
                getStatusText(reviewStatus.status)
              }}</span>
            </div>
            <!-- <div class="status-message" v-if="reviewStatus.message">{{ reviewStatus.message }}</div>
            <div class="status-time" v-if="reviewStatus.estimated_time_remaining !== undefined">
              预计剩余时间: {{ formatTime(reviewStatus.estimated_time_remaining) }}
            </div> -->
          </div>
          <!-- <el-progress 
            :percentage="reviewStatus.progress" 
            :status="getProgressStatus(reviewStatus.status)"
            :striped="reviewStatus.status === 'processing'"
            :striped-flow="reviewStatus.status === 'processing'"
          ></el-progress> -->
        </el-card>
      </div>

      <!-- 评审结果显示 -->
      <div v-else-if="reviewResult" class="review-result">
        <h3>下载评审结果</h3>
        <el-card
          v-for="(item, index) in reviewResult"
          :key="index"
          class="result-card"
        >
          <template #header>
            <div class="card-header">
              <span>{{ item.fileName }}</span>
            </div>
          </template>
          <div
            v-for="(comment, cIndex) in item.comments"
            :key="cIndex"
            class="comment-item"
          >
            <div class="comment-line">行号: {{ comment.line }}</div>
            <div class="comment-content">{{ comment.content }}</div>
          </div>
        </el-card>
      </div>

      <!-- 空状态显示 -->
      <el-empty
        v-else-if="!loading && hasSubmitted && !reviewList"
        description="暂无评审结果"
      ></el-empty>

      <!-- 显示评审结果 -->
      <div v-if="reviewList" class="review-statis">
        <el-button type="primary" @click="handleSubmit" class="submit-btn">提交</el-button>
        <el-table :data="reviewList" @selection-change="handleSelectionChange" ref="multipleTableRef" height="100%">
          <el-table-column type="selection" width="55" />
          <el-table-column v-for="item in reviewCol" :label="item.label" :prop="item.prop" :key="item.prop" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  apiService,
  ApiResponse,
  CodeReviewData,
  ReviewResult,
  ReviewStatus,
} from '../api'
import type { UploadProps, UploadUserFile } from 'element-plus'
import MarkdownIt from 'markdown-it'

// 使用从API导入的ReviewResult接口
type ReviewResultItem = ReviewResult

const fileList = ref<UploadUserFile[]>([])
const loading = ref(false)
const loadingResult = ref(false) // 获取评审结果的加载状态
const loadingStatus = ref(false) // 获取评审进度的加载状态
const hasSubmitted = ref(false)
const reviewResult = ref<ReviewResultItem[] | null>(null)
const resultDir = ref<string>('') // 存储评审结果目录
const reviewStatus = ref<ReviewStatus | null>(null) // 存储评审进度信息
const reviewWord = ref(null); //评审文档
const reviewIsFinish = ref(false);
// const reviewList = ref([
//         {
//           problem_id: 1,
//           location: "AggregationCycleUtils.java:20",
//           description:
//             "公共静态变量未使用final修饰，可能导致线程安全问题；同时常量命名不符合规范，应使用大写字母和下划线",
//           severity: "高",
//           suggestion:
//             "使用final修饰符：将变量名改为符合常量命名规范的大写形式（如CYCLE_CHAIN），并定义为：public static final List<Integer> CYCLE_CHAIN = ..."
//         },
//         {
//           problem_id: 2,
//           location: "AggregationCycleUtils.java:28",
//           description: "使用多个if-else判断，逻辑可读性差，易出错",
//           severity: "中",
//           suggestion: "改用switch-case结构提高可读性和维护性"
//         },
//         {
//           problem_id: 3,
//           location: "AggregationCycleUtils.java:46",
//           description:
//             "方法中未覆盖所有可能的周期类型（如SEVEN_DAYS和ONE_HOUR），逻辑不完整",
//           severity: "高",
//           suggestion: "增加对SEVEN_DAYS和ONE_HOUR的处理逻辑"
//         },
//         {
//           problem_id: 4,
//           location: "AggregationCycleUtils.java:60",
//           description:
//             "未处理sourceCycle或targetCycle不在列表中的情况，可能返回错误结果",
//           severity: "高",
//           suggestion:
//             "增加参数有效性检查，处理不在列表中的情况（例如，检查indexOf返回-1的情况并做处理）"
//         },
//         {
//           problem_id: 5,
//           location: "AggregationCycleUtils.java:15",
//           description:
//             "公开静态字段未使用final修饰，可能导致被修改（与问题1重复但位置不同，需确认是否为同一变量）",
//           severity: "中",
//           suggestion: "添加final修饰符（如果与问题1是同一变量则已处理）"
//         },
//         {
//           problem_id: 6,
//           location: "AggregationCycleUtils.java:15",
//           description:
//             "方法应使用Javadoc注释，但缺少@return标签；同时参数cycle缺少Javadoc注释",
//           severity: "低",
//           suggestion:
//             "在方法Javadoc中添加@param cycle说明参数含义，并添加@return标签说明返回值含义"
//         },
//         {
//           problem_id: 7,
//           location: "AggregationCycleUtils.java:34",
//           description: "方法Javadoc描述不清晰，且@return描述错误",
//           severity: "中",
//           suggestion:
//             "修正Javadoc描述：'比较两个周期的大小，目标周期大于源周期返回正数，小于返回负数，相等返回0'，并修正@return描述"
//         },
//         {
//           problem_id: 8,
//           location: "AggregationCycleUtils.java:12",
//           description:
//             "常量未使用final修饰，可能导致被修改；同时常量命名不符合规范",
//           severity: "高",
//           suggestion:
//             "将变量名改为符合常量命名规范的大写形式（如CYCLE_CHAIN），并添加final修饰符：public static final List<Integer> CYCLE_CHAIN = ..."
//         }
//       ]);
const reviewList = ref(null);
const suggestionCount = ref(0);
const reviewCol = [
  {prop:'description',label:'描述'},
  {prop:'location',label:'位置'},
  {prop:'severity',label:'等级'},
  {prop:'suggestion',label:'建议'}
];

const handleRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
  console.log(file, uploadFiles)
}

const handlePreview: UploadProps['onPreview'] = (uploadFile) => {
  console.log(uploadFile)
}

const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
  ElMessage.warning(
    `最多允许上传5个文件，您一共上传了${files.length + uploadFiles.length}个`
  )
}

const beforeRemove: UploadProps['beforeRemove'] = (uploadFile, uploadFiles) => {
  return ElMessageBox.confirm(
    `Cancel the transfer of ${uploadFile.name} ?`
  ).then(
    () => true,
    () => false
  )
}

const doCodeReview = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先上传文件')
    return
  }

  try {
    loading.value = true
    hasSubmitted.value = true

    // 获取原始文件对象
    const files = fileList.value.map((file) => file.raw)
    // 过滤掉可能的undefined值
    const validFiles = files.filter((file): file is File => file !== undefined)

    // 调用API
    const response = await apiService.codeReview(validFiles)

    // 处理新的API响应格式
    if (response && response.code === 0) {
      // 显示消息
      ElMessage.success(response.message)

      // 存储result_dir
      if (response.data && response.data.result_dir) {
        resultDir.value = response.data.result_dir
        console.log('评审结果目录:', resultDir.value)
        console.log('收到的文件:', response.data.files_received)

        // 开始轮询评审进度
        startPollingStatus()
      }
    } else {
      ElMessage.error(response?.message || '代码评审请求失败')
      reviewResult.value = null
    }
  } catch (error) {
    console.error('代码评审失败:', error)
    ElMessage.error('代码评审失败，请稍后重试')
    reviewResult.value = null
  } finally {
    loading.value = false
  }
}

import axios from 'axios';

const downloadZip = async (url, data, filename) => {
  try {
    const response = await axios.post(url, data, {
      responseType: 'blob', // 关键：告诉 axios 返回的是二进制流
      headers: {
        "accept": "application/zip",
        "Content-Type": "multipart/form-data"
      },
    });

    // 创建 Blob 对象
    const blob = new Blob([response.data], { type: 'application/zip' });

    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = filename || 'download.zip';
    document.body.appendChild(a);
    a.click();

    // 清理
    document.body.removeChild(a);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('下载失败:', error);
  }
};

const md = new MarkdownIt();
const multipleSelection = ref(null);
// 点击勾选
const handleSelectionChange = (val) => {
  multipleSelection.value = val.length;
};

const multipleTableRef = ref();

// 提交
const handleSubmit = async () => {
  const params = {
    "programming_language": "java",
    "suggestion_count":suggestionCount.value,
    "adopted_count": multipleSelection.value
  };
    // 调用API获取评审文档
   const response = await apiService.saveStatistics(params);
   console.log('response', response);
   if (response.code === 0) {
   ElMessage.success('提交成功');
    multipleTableRef.value.clearSelection();
   }
};

// 获取评审总结
const getReview = async () => {
  if (!resultDir.value) {
    ElMessage.warning('没有可用的评审结果');
    return;
  }

  try {
    // 调用API获取评审文档
    const response = await apiService.getReview(resultDir.value);
    reviewList.value = response.problems;
    suggestionCount.value = response.total_problems;
    console.log('aaa',reviewList.value,suggestionCount.value, response);
  } catch (error) {
    ElMessage.error('获取评审文档失败，请稍后重试');
  }
};

// 获取评审文档
const getReviewWord = async () => {
  if (!resultDir.value) {
    ElMessage.warning('没有可用的评审结果');
    return;
  }

   try {
    // 调用API获取评审文档
    const response = await apiService.getReviewWord(resultDir.value);
    reviewWord.value = response;
    Object.keys(response).forEach(i => {
      reviewWord.value[i] = md.render(reviewWord.value[i]);
    });
  } catch (error) {
    console.error('获取评审结果失败:', error)
    ElMessage.error('获取评审文档失败，请稍后重试');
  }
};

// 获取评审结果
const getReviewResult = async () => {
  if (!resultDir.value) {
    ElMessage.warning('没有可用的评审结果')
    return
  }

  try {
    loadingResult.value = true
    hasSubmitted.value = true

    downloadZip('/code-review-api/download', { path: resultDir.value }, '评审结果.zip');
  } catch (error) {
    console.error('获取评审结果失败:', error)
    ElMessage.error('获取评审结果失败，请稍后重试')
  } finally {
    loadingResult.value = false
  }
}
// 获取评审进度
const getReviewStatus = async () => {
  if (!resultDir.value) {
    return
  }

  try {
    loadingStatus.value = true

    // 调用API获取评审进度
    const response = await apiService.getReviewStatus(resultDir.value)

    if (response && response.code === 0 && response.data) {
      reviewStatus.value = response.data

      // 如果评审已完成，停止轮询并获取结果
      if (response.data.status === 'completed') {
        reviewIsFinish.value = true;
        stopPollingStatus()
        // 自动获取评审结果
        // await getReviewResult()
        // await getReviewWord();
        await getReview();
      } else if (response.data.status === 'failed') {
        stopPollingStatus()
        ElMessage.error('代码评审失败')
      }
    }
  } catch (error) {
    console.error('获取评审进度失败:', error)
  } finally {
    loadingStatus.value = false
  }
}

// 轮询间隔（毫秒）
const POLLING_INTERVAL = 10000
let pollingTimer: number | null = null

// 开始轮询评审进度
const startPollingStatus = () => {
  // 先立即获取一次进度
  getReviewStatus()

  // 然后设置定时器定期获取
  pollingTimer = window.setInterval(() => {
    getReviewStatus()
  }, POLLING_INTERVAL)
}

// 停止轮询评审进度
const stopPollingStatus = () => {
  if (pollingTimer !== null) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  stopPollingStatus()
})

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '等待中',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
  }
  return statusMap[status] || status
}

// 获取进度条状态
const getProgressStatus = (
  status: string
): '' | 'success' | 'exception' | 'warning' => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  if (status === 'pending') return 'warning'
  return ''
}

// 格式化时间（秒转为分:秒）
const formatTime = (seconds: number): string => {
  if (seconds < 60) return `${seconds}秒`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}
</script>

<style scoped>
.code-review {
  width: 100%;
  height: 100vh;
  display: flex;
}

.code-review .left {
  width: 350px;
  padding: 20px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e4e7ed;
}

.code-review .right {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.upload-demo {
  margin-bottom: 20px;
}

.review-result {
  width: 100%;
}

.result-card,
.status-card {
  margin-bottom: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.comment-item {
  padding: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-line {
  font-size: 14px;
  color: #409eff;
  margin-bottom: 5px;
}

.comment-content {
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* 评审进度样式 */
.status-info {
  margin-bottom: 15px;
}

.status-label {
  font-weight: bold;
  margin-bottom: 8px;
}

.status-message {
  margin-bottom: 8px;
  color: #606266;
}

.status-time {
  color: #909399;
  font-size: 0.9em;
}

/* 状态颜色 */
.status-pending {
  color: #e6a23c;
}

.status-processing {
  color: #409eff;
}

.status-completed {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.el-button+.el-button {
  margin-left: 0;
}
.review-block {
  margin-top: 24px;
}
.title {
  margin-bottom: 8px;
}
.markdown-content {
  padding: 20px;
  line-height: 1.6;
}
.markdown-content h1,
.markdown-content h2,
.markdown-content h3 {
  margin-top: 1em;
}
.markdown-content pre {
  background: #f4f4f4;
  padding: 10px;
  overflow-x: auto;
}
.markdown-content code {
  background: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
}
.submit-btn {
  float: right;
}
.review-statis {
  height: 100%;
  overflow-y: hidden;
}
</style>