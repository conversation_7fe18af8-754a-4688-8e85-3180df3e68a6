package com.cet.eem.fusion.cost.core.service.impl;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.cost.core.dao.CostCheckItemDao;
import com.cet.eem.fusion.cost.core.dao.CostCheckNodeConfigDao;
import com.cet.eem.fusion.cost.core.model.CostCheckNodeConfig;
import com.cet.eem.fusion.cost.core.service.CostCheckItemService;
import com.cet.eem.fusion.cost.sdk.model.CostCheckItem;
import com.cet.eem.fusion.cost.sdk.model.CostCheckPlan;
import com.cet.eem.fusion.energy.sdk.model.generalrules.FeeScheme;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
@Service
public class CostCheckItemServiceImpl implements CostCheckItemService {
    @Autowired
    CostCheckItemDao costCheckItemDao;

    @Autowired
    CostCheckNodeConfigDao costCheckNodeConfigDao;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<CostCheckItem> queryCostCheckItem(BaseVo node) {
        // 根据节点查询核算方案
        List<CostCheckNodeConfig> costCheckNodeConfigs = costCheckNodeConfigDao.queryCostCheckPlan(node.getId(), node.getModelLabel());
        // 只取第一个关联的核算方案
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return Collections.emptyList();
        }
        Long checkPlanId = costCheckNodeConfigs.get(0).getCostCheckPlanId();

        // 根据核算方案查询核算构成项
        List<CostCheckItem> costCheckItems = costCheckItemDao.getCostCheckItems(checkPlanId);
        if (CollectionUtils.isEmpty(costCheckItems)) {
            return Collections.emptyList();
        }
        return costCheckItems;
    }

    @Override
    public Map<BaseVo, List<CostCheckItem>> queryCostCheckItem(Collection<BaseVo> nodes) {
        // 根据节点查询核算方案
        List<CostCheckNodeConfig> costCheckNodeConfigs = costCheckNodeConfigDao.queryCostCheckPlan(nodes);
        Map<BaseVo, List<CostCheckItem>> result = new HashMap<>(nodes.size());

        // 根据核算方案查询核算构成项
        Set<Long> checkPlanIds = costCheckNodeConfigs.stream().map(CostCheckNodeConfig::getCostCheckPlanId).collect(Collectors.toSet());
        List<CostCheckPlan> costCheckPlans = costCheckItemDao.getCostCheckItems(checkPlanIds);
        if (CollectionUtils.isEmpty(costCheckPlans)) {
            return Collections.emptyMap();
        }

        Map<BaseVo, List<CostCheckNodeConfig>> nodeAndCostPlanMap = costCheckNodeConfigs.stream()
                .collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())));

        nodeAndCostPlanMap.forEach((node, val) -> {
            // 只取第一个关联的核算方案
            Long checkPlanId = val.get(0).getCostCheckPlanId();
            Optional<CostCheckPlan> any = costCheckPlans.stream().filter(it -> Objects.equals(checkPlanId, it.getId())).findAny();
            if (!any.isPresent()) {
                return;
            }

            if (CollectionUtils.isEmpty(any.get().getCostCheckItemModelList())) {
                return;
            }

            result.put(node, any.get().getCostCheckItemModelList());
        });

        return result;
    }

    @Override
    public Collection<Long> queryCostCheckItem(BaseVo node, Collection<Integer> feeRateTypes, Integer energyType) {
        // 根据节点查询
        List<CostCheckItem> costCheckItems = queryCostCheckItem(node);
        Set<Long> feeSchemeIds = costCheckItems.stream().map(CostCheckItem::getFeeSchemeId).collect(Collectors.toSet());

        // 查询费率方案，并根据能源类型过滤
        List<FeeScheme> feeSchemes = modelServiceUtils.query(feeSchemeIds, ModelLabelDef.FEE_SCHEME, FeeScheme.class);
        Set<Long> finalFeeSchemeIds = feeSchemes.stream()
                .filter(it -> feeRateTypes.contains(it.getFeeratetype()) &&
                        Objects.equals(it.getEnergytype(), energyType))
                .map(FeeScheme::getId)
                .collect(Collectors.toSet());

        return costCheckItems.stream()
                .filter(it -> finalFeeSchemeIds.contains(it.getFeeSchemeId())).map(CostCheckItem::getId).collect(Collectors.toSet());
    }

    @Override
    public Map<BaseVo, List<Long>> queryCostCheckItem(Collection<BaseVo> nodes, Collection<Integer> feeRateTypes, Integer energyType) {
        // 根据节点查询
        Map<BaseVo, List<CostCheckItem>> nodeAndCostItemMap = queryCostCheckItem(nodes);
        if (MapUtils.isEmpty(nodeAndCostItemMap)) {
            return Collections.emptyMap();
        }
        Set<Long> feeSchemeIds = nodeAndCostItemMap.values().stream().flatMap(Collection::stream).map(CostCheckItem::getFeeSchemeId).collect(Collectors.toSet());

        // 查询费率方案，并根据能源类型过滤
        List<FeeScheme> feeSchemes = modelServiceUtils.query(feeSchemeIds, ModelLabelDef.FEE_SCHEME, FeeScheme.class);
        Set<Long> finalFeeSchemeIds = feeSchemes.stream()
                .filter(it -> feeRateTypes.contains(it.getFeeratetype()) &&
                        Objects.equals(it.getEnergytype(), energyType))
                .map(FeeScheme::getId)
                .collect(Collectors.toSet());

        Map<BaseVo, List<Long>> result = new HashMap<>();
        nodeAndCostItemMap.forEach((node, val) -> {
            result.put(node, val.stream()
                    .filter(it -> finalFeeSchemeIds.contains(it.getFeeSchemeId())).map(CostCheckItem::getId).collect(Collectors.toList()));
        });
        return result;
    }

    @Override
    public Map<BaseVo, Collection<Long>> queryFeeScheme(Collection<BaseVo> nodes, Collection<Integer> feeRateTypes, Integer energyType) {
        // 根据节点查询
        Map<BaseVo, List<CostCheckItem>> nodeAndCostItemMap = queryCostCheckItem(nodes);
        if (MapUtils.isEmpty(nodeAndCostItemMap)) {
            return Collections.emptyMap();
        }
        Set<Long> feeSchemeIds = nodeAndCostItemMap.values().stream().flatMap(Collection::stream).map(CostCheckItem::getFeeSchemeId).collect(Collectors.toSet());

        // 查询费率方案，并根据能源类型过滤
        List<FeeScheme> feeSchemes = modelServiceUtils.query(feeSchemeIds, ModelLabelDef.FEE_SCHEME, FeeScheme.class);
        Set<Long> finalFeeSchemeIds = feeSchemes.stream()
                .filter(it -> feeRateTypes.contains(it.getFeeratetype()) &&
                        Objects.equals(it.getEnergytype(), energyType))
                .map(FeeScheme::getId)
                .collect(Collectors.toSet());

        Map<BaseVo, Collection<Long>> result = new HashMap<>();
        nodeAndCostItemMap.forEach((node, val) -> {
            result.put(node, val.stream()
                    .map(CostCheckItem::getFeeSchemeId).filter(finalFeeSchemeIds::contains).collect(Collectors.toSet()));
        });
        return result;
    }

}
