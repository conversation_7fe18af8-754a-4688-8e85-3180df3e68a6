import os

from langchain_openai import OpenAI


# 创建大模型
def create_llm_model(model_name, model_url, model_api_key):  # 添加max_tokens参数，默认4096
    return OpenAI(model=model_name,
                  base_url=model_url,
                  api_key=model_api_key,
                  streaming=True,
                  max_tokens=2000,
                  temperature=0.6,
                  # model_kwargs={"enable_thinking": False},
                  )  # 添加max_tokens参数配置

def create_review_llm_model(model_name, model_url, model_api_key):  # 添加max_tokens参数，默认4096
    return OpenAI(model=model_name,
                  base_url=model_url,
                  api_key=model_api_key,
                  max_tokens=2000,
                  temperature=0.6,
                  )
def create_llm_qwen3():
    # model_name = 'deepseek-ai/DeepSeek-V3'
    # model_name = 'deepseek-ai/DeepSeek-R1-Distill-Qwen-32B'
    # model_name = 'deepseek-ai/DeepSeek-R1'

    # DeepSeek模型
    # model_name = 'deepseek-ai/DeepSeek-R1-Distill-Qwen-32B'
    # model_url = 'https://api.siliconflow.cn/v1/'

    # 阿里模型
    # model_name = 'Qwen/Qwen3-32B'
    # model_url = 'https://api.siliconflow.cn/v1/'

    # model_name = 'deepseek-ai/DeepSeek-R1'
    # model_url = 'https://api.siliconflow.cn/v1/'
    # model_api_key = 'sk-etezckxafhokuqmfygrjjrsrtwwtxbmmswbdjgejzoqqzohf'
    model_name = 'qwen3-coder-plus'
    model_url = 'https://dashscope.aliyuncs.com/compatible-mode/v1/'
    model_api_key = 'sk-9f9045d3aa6c41a89a7c9e8eac24c61d'
    # model_name = 'Qwen_Qwen3-32B-AWQ'
    # model_url = 'http://172.17.6.105:40000/v1'

    # model_api_key = os.getenv('GJ_KEY')
    # model_api_key = 'sk-LCR0d3ySIIUZEs1QGqfAtkKBFmR2TrKbClY2m8bKswKZil4G'
    # model_api_key = 'sk-MzEVwjDGaCLUUc1A00iaQb7tD8vD1LsIfGNw3wjgKby1eu8x'
    # max_tokens = 32768  # 新增最大token数配置

    return create_llm_model(model_name, model_url, model_api_key)  # 添加max_tokens参数

def review_create_llm_qwen3():
    # model_name = 'Pro/deepseek-ai/DeepSeek-V3'
    # model_url = 'https://api.siliconflow.cn/v1/'
    # model_api_key = 'sk-etezckxafhokuqmfygrjjrsrtwwtxbmmswbdjgejzoqqzohf'
    model_name = 'qwen3-coder-plus'
    model_url = 'https://dashscope.aliyuncs.com/compatible-mode/v1/'
    model_api_key = 'sk-9f9045d3aa6c41a89a7c9e8eac24c61d'
    return create_review_llm_model(model_name, model_url, model_api_key)  # 添加max_tokens参数
