import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  server: {
    host: '0.0.0.0',
    proxy: {
      // 代码评审API代理
      "/code-review-api": {
        target: "http://************:8080",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/code-review-api/, "/api")
      },
    },
  },
});
