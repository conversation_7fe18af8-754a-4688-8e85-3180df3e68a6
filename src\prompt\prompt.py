import os

def load_prompt_template() -> list:
    """
    读取 resources/prompt 目录下的所有.md文件内容，并返回一个结构体列表
    
    返回:
        list: 包含字典的列表，每个字典包含:
              - rule_name: 文件名(不含后缀)
              - prompt_content: 文件内容
        
    异常:
        FileNotFoundError: 当提示词目录不存在时抛出
        IOError: 当文件读取失败时抛出
    """
    current_dir = os.path.dirname(__file__)  # 获取当前文件所在目录
    # 构造 resources/prompt 目录的绝对路径
    resources_dir = os.path.abspath(os.path.join(current_dir, "../..", "resources", "prompt"))
    
    if not os.path.exists(resources_dir):
        raise FileNotFoundError(
            f"提示词目录 {resources_dir} 未找到，请检查目录路径"
        )
    
    # 修改过滤条件，只获取.md文件
    prompt_files = [f for f in os.listdir(resources_dir) 
                   if os.path.isfile(os.path.join(resources_dir, f)) and f.endswith('.txt')]
    
    if not prompt_files:
        raise FileNotFoundError(
            f"提示词目录 {resources_dir} 下没有.md文件，请检查目录内容"
        )
    
    prompts = []
    for file_name in prompt_files:
        file_path = os.path.join(resources_dir, file_name)
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                prompts.append({
                    "rule_name": os.path.splitext(file_name)[0],  # 去除文件后缀
                    "prompt_content": f.read()
                })
        except IOError as e:
            raise IOError(
                f"读取提示词文件失败: {str(e)}，请检查文件权限"
            )
    
    return prompts