Assistant: Java安全代码扫描-LossConfigServiceImpl.java
| 问题编号 | 问题代码行 | 问题代码 | 问题描述 | 问题类别 | 问题级别 | 修改建议 |
|----------|------------|--------------|------|----------|----------|------|
| 1 | 1 | `package com.cet.eem.fusion.loss.core.service.impl;` | 类注释中未包含功能描述、作者信息和日期信息 | 强制 | 高 | 在类注释中添加功能描述、作者信息和日期信息 |
| 2 | 50 | `@Resource` | 字段注入应使用构造器或setter方法注入 | 建议 | 中 | 将`@Resource`改为构造器注入或setter方法注入 |
| 3 | 81 | `powerConfigProcess(finalConfig, node);` | 方法名`powerConfigProcess`使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`processPowerConfig` |
| 4 | 122 | `if (CollectionUtils.isEmpty(baseVos)) { return baseVos; }` | 方法体长度超过80行（实际为123行） | 强制 | 高 | 将方法拆分为多个小方法 |
| 5 | 122 | `sortNodes` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`sortNodesByLabelAndId` |
| 6 | 155 | `getLossGroup` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`retrieveLossGroup` |
| 7 | 181 | `getSystemEvents` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`querySystemEvents` |
| 8 | 214 | `genLossEventQueryParam` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`generateLossEventQueryParams` |
| 9 | 259 | `exportSystemEvents` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`exportSystemEventData` |
| 10 | 283 | `genSystemEventValue` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`generateSystemEventValues` |
| 11 | 328 | `updateLossEvent` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`updateLossEventStatus` |
| 12 | 349 | `writeToRedis` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`writeEventToRedis` |
| 13 | 354 | `genSystemEventVos` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`generateSystemEventVos` |
| 14 | 411 | `getLossLevelNodeTree` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`retrieveLossLevelNodeTree` |
| 15 | 441 | `getRelationProperties` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`retrieveRelationProperties` |
| 16 | 462 | `powerConfigProcessWithLossGroup` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`processPowerConfigWithLossGroup` |
| 17 | 482 | `assembleLossConfigData` | 方法名使用驼峰命名法，但不符合动词开头的规范 | 强制 | 低 | 将方法名改为`assembleLossConfigurationData` |

| 问题级别 | 问题个数 |
|--------|---------|
| 高 | 2 |
| 中 | 1 |
| 低 | 14 |